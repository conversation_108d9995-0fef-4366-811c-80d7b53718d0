package com.asmtunis.procaisseinventory.data.unite.local.repository

import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.data.unite.local.dao.UniteDAO
import kotlinx.coroutines.flow.Flow


class UniteLocalRepositoryImpl(private val uniteDAO: UniteDAO) :
    UniteLocalRepository {
    override fun upsertAll(value: List<Unite>) = uniteDAO.insertAll(value)

    override fun deleteAll() = uniteDAO.deleteAll()

    override fun getAll(): Flow<List<Unite>> = uniteDAO.all
}