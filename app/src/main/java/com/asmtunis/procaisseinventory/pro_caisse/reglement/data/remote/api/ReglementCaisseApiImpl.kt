package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementUpdate
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class ReglementCaisseApiImpl(private val client: HttpClient) : ReglementCaisseApi {


    override suspend fun getReglementCaisseByTicket(baseConfig: String): Flow<DataResult<List<ReglementCaisse>>> = flow {
        val result = executePostApiCall<List<ReglementCaisse>>(
            client = client,
            endpoint = Urls.GET_REGLEMENT_CAISSE_BY_TICKET,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getReglementCaisseByTickets(baseConfig: String): Flow<DataResult<List<List<ReglementCaisse>>>> = flow {

        val result = executePostApiCall<List<List<ReglementCaisse>>>(
            client = client,
            endpoint = Urls.GET_REGLEMENT_CAISSE_BY_TICKETS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getReglementCaisseBySession(
        baseConfig: String,
        exercice: String,
        archive: Boolean
    ): Flow<DataResult<List<ReglementCaisse>>> = flow {

        val queryParams = mapOf(
            "exercice" to exercice,
            "archive" to archive
        )
        val result = executePostApiCall<List<ReglementCaisse>>(
            client = client,
            endpoint = Urls.GET_REGLEMENT_CAISSE_BY_SESSION,
            queryParams = queryParams,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun addBatchPayments(baseConfig: String): Flow<DataResult<List<ReglementUpdate>>> = flow {
        val result = executePostApiCall<List<ReglementUpdate>>(
            client = client,
            endpoint = Urls.ADD_BATCH_PAYMENTS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }