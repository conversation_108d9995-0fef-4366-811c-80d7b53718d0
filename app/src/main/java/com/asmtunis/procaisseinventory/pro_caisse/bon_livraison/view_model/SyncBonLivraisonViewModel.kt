package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.TICKET_FACTURE_DEJA
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.event.TicketSyncEvent
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.AUTO_FACTURE_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.timbersValueSum
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.delay
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@HiltViewModel
class SyncBonLivraisonViewModel
    @Inject
    constructor(
        @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
        @IoDispatcher val dispatcherIO: CoroutineDispatcher,
        @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
        @MainDispatcher val mainDispatcher: CoroutineDispatcher,
        val proCaisseRemote: ProCaisseRemote,
        val proCaisseLocalDb: ProCaisseLocalDb,
        private val listenNetwork: ListenNetwork,
        private val dataStoreRepository: DataStoreRepository,
        // app: Application
    ) : ViewModel() {


    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private var listActifTimberFlow = proCaisseLocalDb.timbre.getActif().distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    var responseAddBatchTicketWithLignesState: RemoteResponseState<List<TicketUpdate>> by mutableStateOf(RemoteResponseState())
        private set
    var notSyncAddBatchTicketWithLignesObj : String by mutableStateOf("")
        private set
    var responseFactureTicket: RemoteResponseState<List<TicketUpdate>> by mutableStateOf(RemoteResponseState())
        private set

    var ticketsWithLinesAndPaymentsNotSync: List<TicketWithFactureAndPayments> by mutableStateOf(emptyList())
        private set
        init {
        getNotSyncBonLivraison()
                }


     var listActifTimber by  mutableStateOf(emptyList<Timbre>())
        private set


        fun getNotSyncBonLivraison() {
            viewModelScope.launch(dispatcherIO) { // Utiliser dispatcherIO pour les opérations de base de données
                try {
                    // Réinitialiser la liste des tickets non synchronisés
                    ticketsWithLinesAndPaymentsNotSync = emptyList()

                    // Récupérer les tickets non synchronisés
                    val bonLivraisonNotSyncFlow = proCaisseLocalDb.bonLivraison.notSynced().distinctUntilChanged()

                    // Combiner les flux pour obtenir les informations nécessaires
                    combine(networkFlow, bonLivraisonNotSyncFlow, autoSyncFlow, listActifTimberFlow) { isConnected, inventaireNotSyncList, autoSync, listActifTimberFlow ->
                        // Mettre à jour les variables d'état
                        listActifTimber = listActifTimberFlow.ifEmpty { emptyList() }
                        connected = isConnected
                        autoSyncState = autoSync

                        // Retourner la liste des tickets non synchronisés
                        inventaireNotSyncList?.ifEmpty { emptyList() }?: emptyList()
                    }.collect { tickets ->
                        // Mettre à jour la liste des tickets non synchronisés
                        Log.d("SyncBonLivraisonViewModel", "Tickets non synchronisés: ${tickets.size}")

                        // Mettre à jour la liste des tickets non synchronisés
                        ticketsWithLinesAndPaymentsNotSync = tickets

                        // Si la connexion est disponible et la synchronisation automatique est activée, synchroniser les tickets
                        if(connected && autoSyncState && tickets.isNotEmpty()) {
                            Log.d("SyncBonLivraisonViewModel", "Synchronisation automatique des tickets")
                            syncBonLivraison()
                        }
                    }
                } catch (e: Exception) {
                    Log.e("SyncBonLivraisonViewModel", "Erreur lors de la récupération des tickets non synchronisés: ${e.message}")
                }
            }
        }


    private fun setFactureTimbreAndRevImp() {

        val timberValue = timbersValueSum(listActifTimber)

        ticketsWithLinesAndPaymentsNotSync.forEach {ticketsWithLinesAndPayments->
            val client = ticketsWithLinesAndPayments.client
            val ticket = ticketsWithLinesAndPayments.ticket

//                 if ((client?.cltMntRevImp ?: 0.0) > 0.0) {
//
//                     var mntTTC = stringToDouble(ticket?.tIKMtTTC) * (1 + (client?.cltMntRevImp ?: 0.0) / 100)
//
//                     if(stringToDouble(client?.cLITimbre) == 0.0) {
//                         mntTTC = timberValue
//                     }
//
//                    ticketsWithLinesAndPayments.ticket = ticket?.copy(
//                       mntRevImp = stringToDouble(ticket.tIKMtTTC) * ((client?.cltMntRevImp ?: 0.0) / 100),
//                        tIKMtTTC = convertDoubleToDoubleFormat(mntTTC),
//                        tIKTimbre =  if (stringToDouble(client?.cLITimbre?:"0") == 0.0) "0"
//                        else listActifTimber.first().tIMBCode
//                    )
//                }


            ticketsWithLinesAndPayments.ticket = ticket?.copy(
                tIKTimbre =  if (stringToDouble(client?.cLITimbre?:"0") == 0.0) "0" else listActifTimber.first().tIMBCode
            )
        }

    }


        fun syncBonLivraison(selectedTicket: TicketWithFactureAndPayments = TicketWithFactureAndPayments()) {
            viewModelScope.launch(dispatcherIO) {
                val autoFacture = proCaisseLocalDb.dataStore.getBoolean(AUTO_FACTURE_AUTHORISATION).first()

                if(autoFacture) {
                    setFactureTimbreAndRevImp()
                }

                // Récupérer l'ID de la session de caisse active
                val activeSessionId = getActiveSessionId()
                Log.d("SyncBonLivraisonViewModel", "Synchronisation avec la session de caisse active: $activeSessionId")

                // Ajouter l'ID de session aux tickets à synchroniser
                if (activeSessionId.isNotEmpty()) {
                    if (selectedTicket != TicketWithFactureAndPayments()) {
                        // Mettre à jour le ticket sélectionné
                        selectedTicket.ticket?.let { ticket ->
                            val updatedTicket = ticket.copy(tIKIdSCaisse = activeSessionId)
                            selectedTicket.ticket = updatedTicket
                        }
                    } else {
                        // Mettre à jour tous les tickets non synchronisés
                        ticketsWithLinesAndPaymentsNotSync.forEach { ticketWithLines ->
                            ticketWithLines.ticket?.let { ticket ->
                                val updatedTicket = ticket.copy(tIKIdSCaisse = activeSessionId)
                                ticketWithLines.ticket = updatedTicket
                            }
                        }
                    }
                }

                val baseConfigObj =
                    GenericObject(
                        proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                            ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                        Json.encodeToJsonElement(if(selectedTicket != TicketWithFactureAndPayments()) listOf(selectedTicket)  else ticketsWithLinesAndPaymentsNotSync),
                    )


                notSyncAddBatchTicketWithLignesObj = Json.encodeToString(baseConfigObj)
                proCaisseRemote.ticket.addBatchTicketWithLignesTicketAndPayment(
                    baseConfig = notSyncAddBatchTicketWithLignesObj,
                    autoFacture = autoFacture,
                ).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            responseAddBatchTicketWithLignesState = RemoteResponseState(data = result.data, loading = false, error = null)

                            // Vérifier si la réponse est vide
                            if (result.data.isNullOrEmpty()) {
                                Log.w("SyncBonLivraisonViewModel", "Réponse serveur vide - traitement des tickets comme synchronisés avec succès")

                                // Si la réponse est vide, considérer que la synchronisation a réussi
                                // Marquer tous les tickets comme synchronisés
                                val ticketsToSync = if(selectedTicket != TicketWithFactureAndPayments()) listOf(selectedTicket) else ticketsWithLinesAndPaymentsNotSync

                                ticketsToSync.forEach { ticketWithLines ->
                                    ticketWithLines.ticket?.let { ticket ->
                                        Log.d("SyncBonLivraisonViewModel", "Marquage du ticket ${ticket.tIKNumTicketM} comme synchronisé")

                                        // Marquer le ticket comme synchronisé
                                        proCaisseLocalDb.bonLivraison.updateBLNumber(
                                            tikNumTicket = ticket.tIKNumTicket.toIntOrNull() ?: 0,
                                            tikNumTicketM = ticket.tIKNumTicketM,
                                            tikDdm = getCurrentDateTime(),
                                        )

                                        // Associer à la session de caisse active si nécessaire
                                        if (activeSessionId.isNotEmpty()) {
                                            proCaisseLocalDb.bonLivraison.updateSessionId(
                                                tikNumTicketM = ticket.tIKNumTicketM,
                                                sessionId = activeSessionId
                                            )
                                        }
                                    }
                                }

                                // Rafraîchir la liste des tickets non synchronisés
                                getNotSyncBonLivraison()

                                // Émettre un événement de synchronisation
                                withContext(mainDispatcher) {
                                    TicketSyncEvent.triggerSync()
                                }

                                return@onEach
                            }

                            for (i in result.data.indices) {

                                val ticketUpdate = result.data[i]
                                Log.d("SyncBonLivraisonViewModel", "Code de réponse: ${ticketUpdate.code}, Message: ${ticketUpdate.message}")

                                // Gérer les différents codes de réponse
                                when (ticketUpdate.code) {
                                    "10200", "10201" -> {
                                        // Succès ou mise à jour réussie
                                        updateLocalData(ticketUpdate = ticketUpdate, insertFacture = autoFacture)
                                    }
                                    "10302" -> {
                                        // Ticket n'existe pas - essayer de le créer
                                        Log.d("SyncBonLivraisonViewModel", "Ticket n'existe pas, tentative de création")
                                        // Utiliser une méthode spécifique pour gérer ce cas
                                        handleTicketNotExists(ticketUpdate = ticketUpdate, insertFacture = autoFacture)
                                    }
                                    else -> {
                                        // Autres erreurs
                                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = false, error = ticketUpdate.message)

                                        // Mettre à jour le message d'erreur dans la base de données locale
                                        ticketUpdate.tIKNumTicketM?.let { tikNumTicketM ->
                                            proCaisseLocalDb.bonLivraison.updateSyncErrorMsg(
                                                tikNumTicketM = tikNumTicketM,
                                                errorMsg = ticketUpdate.message ?: "UnkownError",
                                            )
                                        }
                                    }
                                }
                            }
                        }

                        is DataResult.Loading -> {
                            responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedTicket.ticket?.tIKNumTicket)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }

        fun facturerBL(ticketWithFactureAndPayments: TicketWithFactureAndPayments) {

            ticketsWithLinesAndPaymentsNotSync = emptyList()
            ticketsWithLinesAndPaymentsNotSync = listOf(ticketWithFactureAndPayments)
            setFactureTimbreAndRevImp()

            viewModelScope.launch(dispatcherIO) {
                val baseConfigObj =
                    GenericObject(
                        proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                            ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                        Json.encodeToJsonElement(ticketsWithLinesAndPaymentsNotSync),
                    )

                proCaisseRemote.ticket.addBatchFactureWithLines(baseConfig = Json.encodeToString(baseConfigObj)).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            responseFactureTicket = RemoteResponseState(data = result.data, loading = false, error = null)
                            ticketsWithLinesAndPaymentsNotSync = emptyList()
                            for (i in result.data!!.indices) {
                                val ticketUpdate = result.data[i]
                                if (!ticketUpdate.code.equals("10200") && !ticketUpdate.code.equals("10201") && !ticketUpdate.code.equals("10304")) {
                                    // TODO HANDLE OTHER CASES HERE
                                    return@onEach
                                }

                                updateLocalData(ticketUpdate = ticketUpdate, insertFacture = true)
                            }
                        }

                        is DataResult.Loading -> {
                            responseFactureTicket = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            ticketsWithLinesAndPaymentsNotSync = emptyList()
                            responseFactureTicket = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }

        private fun updateClientSold(
            cLICode: String,
            ticketUpdate: TicketUpdate,
        )  {
            viewModelScope.launch(dispatcherIO) {
                proCaisseLocalDb.clients.updateMoneyClient(
                    codeClt = cLICode,
                    soldClient = ticketUpdate.soldeClient?: "",
                    cliCredit = ticketUpdate.credit?: "",
                    cliDebit = ticketUpdate.debit?: "",
                )
            }
        }

        private fun updateLocalData(ticketUpdate: TicketUpdate, insertFacture: Boolean, createIfNotExists: Boolean = false) {
            // Récupérer l'ID de la session de caisse active
            viewModelScope.launch(dispatcherIO) { // Utiliser dispatcherIO pour les opérations de base de données
                val activeSessionId = getActiveSessionId()
                Log.d("SyncBonLivraisonViewModel", "Session de caisse active: $activeSessionId")
                updateTicketWithSessionId(ticketUpdate, insertFacture, createIfNotExists, activeSessionId)
            }
        }

        suspend fun getActiveSessionId(): String {
            // Récupérer la session de caisse active depuis la base de données locale
            return try {
                val sessions = proCaisseLocalDb.sessionCaisse.getAll().first()
                val activeSession = sessions.firstOrNull { it.sCClotCaisse == 0 } // Session non clôturée
                activeSession?.sCIdSCaisse ?: ""
            } catch (e: Exception) {
                Log.e("SyncBonLivraisonViewModel", "Erreur lors de la récupération de la session active: ${e.message}")
                ""
            }
        }

        private suspend fun updateTicketWithSessionId(ticketUpdate: TicketUpdate, insertFacture: Boolean, createIfNotExists: Boolean, sessionId: String) {
            val ddm = getCurrentDateTime()

            // Insérer la facture si nécessaire
            if(insertFacture) {
                ticketUpdate.facture?.let { proCaisseLocalDb.facture.upsert(it) }
            }

            // Mettre à jour les lignes de ticket avec le nouveau numéro de ticket
            proCaisseLocalDb.ligneBonLivraison.updateNumTicket(
                codeM = ticketUpdate.tIKNumTicketM?: "",
                newCode = ticketUpdate.tIKNumTicket,
                exercice = ticketUpdate.tIKExerc?: "",
                carnet = ticketUpdate.tIKIdCarnet?: "",
            )

            // Mettre à jour le bon de livraison avec les informations du ticket et l'associer à la session de caisse
            if (createIfNotExists && sessionId.isNotEmpty()) {
                // Si le ticket n'existe pas et qu'on a une session active, on le crée
                Log.d("SyncBonLivraisonViewModel", "Création d'un nouveau ticket associé à la session $sessionId")

                // Créer un nouveau ticket avec l'ID de session
                val newTicket = Ticket(
                    tIKNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    tIKExerc = ticketUpdate.tIKExerc ?: "",
                    tIKIdCarnet = ticketUpdate.tIKIdCarnet ?: "",
                    tIKNumTicket = ticketUpdate.tIKNumTicket.toString(),
                    tIKDDm = ddm,
                    tIKDateHeureTicket = getCurrentDateTime(),
                    tIKCodClt = ticketUpdate.codeClient ?: "",
                    tIKNomClient = "", // À remplir si disponible
                    tIKMtTTC = "0", // À mettre à jour avec les lignes de ticket
                    tIKMtHT = "0",
                    tIKMtTVA = "0",
                    tIKMtRemise = "0",
                    tIKEtat = "SELECTED",
                    tIKNumFact = "",
                    tIKStation = "", // À remplir si disponible
                    tIKTimbre = "0",
                    tIKIdSCaisse = sessionId // Associer à la session de caisse active
                )

                // Insérer le nouveau ticket dans la base de données locale
                proCaisseLocalDb.bonLivraison.upsert(newTicket)

                // Mettre à jour les informations du ticket
                if (ticketUpdate.tIKNumeroBL == null) {
                    // Auto facture == true
                    proCaisseLocalDb.bonLivraison.updateBLNumber(
                        tikNumTicket = ticketUpdate.tIKNumTicket,
                        tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                        tikDdm = ddm,
                    )
                } else {
                    // Auto facture == false
                    proCaisseLocalDb.bonLivraison.updateTicketNumber(
                        tikNumBl = ticketUpdate.tIKNumeroBL ?: "",
                        tikNumTicket = ticketUpdate.tIKNumTicket,
                        tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                        tikDdm = ddm,
                    )
                }

                // Mettre à jour l'ID de session de caisse pour ce ticket
                proCaisseLocalDb.bonLivraison.updateSessionId(
                    tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    sessionId = sessionId
                )
            } else {
                // Mise à jour normale du ticket existant
                if (ticketUpdate.tIKNumeroBL == null) {
                    // Auto facture == true
                    proCaisseLocalDb.bonLivraison.updateBLNumber(
                        tikNumTicket = ticketUpdate.tIKNumTicket,
                        tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                        tikDdm = ddm,
                    )
                } else {
                    // Auto facture == false
                    proCaisseLocalDb.bonLivraison.updateTicketNumber(
                        tikNumBl = ticketUpdate.tIKNumeroBL ?: "",
                        tikNumTicket = ticketUpdate.tIKNumTicket,
                        tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                        tikDdm = ddm,
                    )
                }

                // Si on a une session active, mettre à jour l'ID de session pour ce ticket
                if (sessionId.isNotEmpty()) {
                    proCaisseLocalDb.bonLivraison.updateSessionId(
                        tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                        sessionId = sessionId
                    )
                }
            }

            // Mettre à jour les observations si nécessaire
            ticketUpdate.observation?.let { observation ->
                if (observation.isNotEmpty()) {
                    proCaisseLocalDb.bonCommande.updateObservation(
                        devObservation = observation,
                        devNum = ticketUpdate.tIKNumTicketM?: "",
                        exercie = ticketUpdate.tIKExerc?: "",
                    )
                }
            }

            // Gérer le message de ticket déjà facturé
            ticketUpdate.message?.let { message ->
                if (message == TICKET_FACTURE_DEJA) {
                    proCaisseLocalDb.bonCommande.updateObservation(
                        devObservation = message,
                        devNum = ticketUpdate.tIKNumTicketM?: "",
                        exercie = ticketUpdate.tIKExerc?: "",
                    )
                }
            }

            // Mettre à jour le solde client
            updateClientSold(
                cLICode = ticketUpdate.codeClient?: "",
                ticketUpdate = ticketUpdate,
            )

            // Mettre à jour les règlements associés au ticket
            proCaisseLocalDb.reglementCaisse.updateRegCodeAndState(
                regCode = ticketUpdate.tIKNumTicketM?: "",
                regCodeM = ticketUpdate.tIKNumTicketM?: "",
            )

            // Mettre à jour les chèques associés au ticket
            proCaisseLocalDb.chequeCaisse.updateRegCodeAndState(
                regCode = ticketUpdate.tIKNumTicketM?: "",
                regCodeM = ticketUpdate.tIKNumTicketM?: "",
            )

            // Mettre à jour les tickets resto associés au ticket
            proCaisseLocalDb.ticketResto.updateRegCodeAndState(
                regCode = ticketUpdate.tIKNumTicketM?: "",
                regCodeM = ticketUpdate.tIKNumTicketM?: "",
            )

            // Forcer la mise à jour du ticket dans le journal de caisse
            if (sessionId.isNotEmpty() && createIfNotExists) {
                Log.d("SyncBonLivraisonViewModel", "Forcer la mise à jour du ticket dans le journal de caisse")
                // Mettre à jour le statut du ticket pour qu'il apparaisse dans le journal de caisse
                proCaisseLocalDb.bonLivraison.updateTicketStatus(
                    tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    status = "JOURNAL_CAISSE"
                )
            }

            // Forcer la mise à jour des listes de tickets pour rafraîchir l'UI
            viewModelScope.launch(dispatcherIO) {
                try {
                    // Attendre un peu pour que les mises à jour de la base de données soient terminées
                    delay(500)

                    // Log pour le débogage
                    Log.d("SyncBonLivraisonViewModel", "Mise à jour des tickets après synchronisation")

                    // Vérifier si le ticket a été correctement mis à jour
                    try {
                        val tickets = proCaisseLocalDb.bonLivraison.ticketWithLinesAndPaymentByNumTicket(ticketUpdate.tIKNumTicketM ?: "").first()
                        Log.d("SyncBonLivraisonViewModel", "Ticket après mise à jour: ${tickets.ticket?.tIKNumTicket}, isSync: ${tickets.ticket?.isSync}, status: ${tickets.ticket?.status}")
                    } catch (e: Exception) {
                        Log.d("SyncBonLivraisonViewModel", "Ticket non trouvé après mise à jour, mais c'est normal pour un nouveau ticket")
                    }

                    // Récupérer les tickets non synchronisés pour mettre à jour l'UI
                    getNotSyncBonLivraison()

                    // Émettre un événement pour informer les autres ViewModels de la synchronisation
                    Log.d("SyncBonLivraisonViewModel", "Émission d'un événement de synchronisation des tickets")
                    // Basculer sur le thread principal pour émettre l'événement
                    withContext(mainDispatcher) {
                        TicketSyncEvent.triggerSync()
                    }
                } catch (e: Exception) {
                    Log.e("SyncBonLivraisonViewModel", "Erreur lors de la mise à jour des tickets après synchronisation: ${e.message}")
                }
            }
        }
    }
