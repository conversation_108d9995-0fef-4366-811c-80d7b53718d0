package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMission
import kotlinx.coroutines.flow.Flow


@Dao
interface LigneOrdreMissionDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(ligneOrdreMissions: List<LigneOrdreMission>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(ligneOrdreMissions: LigneOrdreMission)

    @Update
    fun updateLigneOrdreMission(ligneOrdreMission: LigneOrdreMission)

    @Query("Delete from ORDRE_MISSION_LIGNE")
    fun deleteAll()

    @get:Query("SELECT count(*) FROM ORDRE_MISSION_LIGNE where isSync=0")
    val noSyncCountMutable: Flow<Int?>?

    @get:Query("SELECT * FROM ORDRE_MISSION_LIGNE where isSync=0")
    val notSync: Flow<List<LigneOrdreMission>?>


    @Query("UPDATE ORDRE_MISSION_LIGNE SET isSync = 1, Status= 'SELECTED'  where LIGOR_Code = :oRDCode")
    fun updateLgOrdreMissionNotSync(oRDCode: String)
    @Query("SELECT * FROM ORDRE_MISSION_LIGNE where LIGOR_Code= :LIGORCode")
    fun getLigneOrdremissionByCode(LIGORCode: String?): Flow<List<LigneOrdreMission?>?>
}

