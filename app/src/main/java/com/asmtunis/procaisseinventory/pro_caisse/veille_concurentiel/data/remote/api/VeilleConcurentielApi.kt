package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJointAddResponse
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.DeleteDataResponseVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ResponseBatchDataVc
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import kotlinx.coroutines.flow.Flow


interface VeilleConcurentielApi {



    suspend fun getVCListeConcurrent(baseConfig: String): Flow<DataResult<List<ConcurrentVC>>>
    suspend fun getVCNewProduct(baseConfig: String): Flow<DataResult<List<NewProductVC>>>
    suspend fun getVCPromo(baseConfig: String): Flow<DataResult<List<PromoVC>>>
    suspend fun getVCPrix(baseConfig: String): Flow<DataResult<List<PrixVC>>>
    suspend fun getVCAutre(baseConfig: String): Flow<DataResult<List<AutreVC>>>
    suspend fun getVCTypeCommunication(baseConfig: String): Flow<DataResult<List<TypeCommunicationVC>>>
    suspend fun getVCImage(baseConfig: String): Flow<DataResult<List<ImagePieceJoint>>>
    suspend fun addBatchVCImage(baseConfig: String): Flow<DataResult<List<ImagePieceJointAddResponse>>>
    suspend fun addBatchDataVConcu(baseConfig: String, table : String): Flow<DataResult<List<ResponseBatchDataVc>>>
    suspend fun deleteDataVConcu(baseConfig: String, table : String): Flow<DataResult<List<DeleteDataResponseVC>>>

}