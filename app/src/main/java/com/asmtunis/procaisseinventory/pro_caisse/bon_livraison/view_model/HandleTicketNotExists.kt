package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model

import android.util.Log
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.event.TicketSyncEvent
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import com.simapps.ui_kit.utils.getCurrentDateTime
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.delay

/**
 * Extension function pour SyncBonLivraisonViewModel
 * Méthode spécifique pour gérer le cas où le ticket n'existe pas sur le serveur (code 10302)
 * Cette méthode crée directement un ticket dans le journal de caisse avec toutes les informations nécessaires
 */
fun SyncBonLivraisonViewModel.handleTicketNotExists(ticketUpdate: TicketUpdate, insertFacture: Boolean) {
    viewModelScope.launch(dispatcherIO) {
        try {
            Log.d("SyncBonLivraisonViewModel", "Création d'un nouveau ticket pour le journal de caisse")
            
            // Récupérer l'ID de la session de caisse active
            val activeSessionId = getActiveSessionId()
            if (activeSessionId.isEmpty()) {
                Log.e("SyncBonLivraisonViewModel", "Impossible de créer le ticket: aucune session de caisse active")
                return@launch
            }
            
            Log.d("SyncBonLivraisonViewModel", "Session de caisse active: $activeSessionId")
            
            // Créer un nouveau ticket avec l'ID de session
            val newTicket = Ticket(
                tIKNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                tIKExerc = ticketUpdate.tIKExerc ?: "",
                tIKIdCarnet = ticketUpdate.tIKIdCarnet ?: "",
                tIKNumTicket = ticketUpdate.tIKNumTicket.toString(),
                tIKDDm = getCurrentDateTime(),
                tIKDateHeureTicket = getCurrentDateTime(),
                tIKCodClt = ticketUpdate.codeClient ?: "",
                tIKNomClient = "", // À remplir si disponible
                tIKMtTTC = "0", // À mettre à jour avec les lignes de ticket
                tIKMtHT = "0",
                tIKMtTVA = "0",
                tIKMtRemise = "0",
                tIKEtat = "JOURNAL_CAISSE", // Important: utiliser JOURNAL_CAISSE pour qu'il apparaisse dans le journal
                tIKNumFact = "",
                tIKStation = "", // À remplir si disponible
                tIKTimbre = "0",
                tIKIdSCaisse = activeSessionId // Associer à la session de caisse active
            )
            
            // Mettre à jour les propriétés de BaseModel
            newTicket.isSync = true
            newTicket.status = "JOURNAL_CAISSE" // Important: utiliser JOURNAL_CAISSE pour qu'il apparaisse dans le journal
            
            // Insérer le nouveau ticket dans la base de données locale
            proCaisseLocalDb.bonLivraison.upsert(newTicket)
            
            Log.d("SyncBonLivraisonViewModel", "Nouveau ticket créé avec succès: ${newTicket.tIKNumTicketM}")
            
            // Mettre à jour les lignes de ticket avec le nouveau numéro de ticket
            proCaisseLocalDb.ligneBonLivraison.updateNumTicket(
                codeM = ticketUpdate.tIKNumTicketM ?: "",
                newCode = ticketUpdate.tIKNumTicket,
                exercice = ticketUpdate.tIKExerc ?: "",
                carnet = ticketUpdate.tIKIdCarnet ?: "",
            )
            
            // Mettre à jour explicitement le statut du ticket pour qu'il apparaisse dans le journal de caisse
            proCaisseLocalDb.bonLivraison.updateTicketStatus(
                tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                status = "JOURNAL_CAISSE"
            )
            
            // Mettre à jour explicitement l'ID de session pour ce ticket
            proCaisseLocalDb.bonLivraison.updateSessionId(
                tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                sessionId = activeSessionId
            )
            
            // Insérer la facture si nécessaire
            if (insertFacture) {
                ticketUpdate.facture?.let { proCaisseLocalDb.facture.upsert(it) }
            }
            
            // Forcer la mise à jour des listes de tickets pour rafraîchir l'UI
            delay(500) // Attendre un peu pour que les mises à jour de la base de données soient terminées
            
            // Récupérer les tickets non synchronisés pour mettre à jour l'UI
            getNotSyncBonLivraison()
            
            // Émettre un événement pour informer les autres ViewModels de la synchronisation
            Log.d("SyncBonLivraisonViewModel", "Émission d'un événement de synchronisation des tickets")
            withContext(mainDispatcher) {
                TicketSyncEvent.triggerSync()
            }
        } catch (e: Exception) {
            Log.e("SyncBonLivraisonViewModel", "Erreur lors de la création du ticket: ${e.message}")
        }
    }
}
