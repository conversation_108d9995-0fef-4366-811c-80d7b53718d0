package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.repository


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import kotlinx.coroutines.flow.Flow



interface TypeCommunicationVCLocalRepository {
    fun upsertAll(value: List<TypeCommunicationVC>)
    fun deleteAll()
    fun getAll(): Flow<List<TypeCommunicationVC>?>

}