package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.ligne_bon_retour

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import kotlinx.coroutines.flow.Flow

interface LigneBonRetourApi {

    suspend fun getLigneBonRetours(baseConfig: String): Flow<DataResult<List<LigneBonRetour>>>
    suspend fun addBatchLigneBonRetours(baseConfig: String): Flow<DataResult<List<LigneBonRetour>>>

}