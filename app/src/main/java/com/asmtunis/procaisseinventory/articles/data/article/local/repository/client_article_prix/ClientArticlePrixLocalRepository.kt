package com.asmtunis.procaisseinventory.articles.data.article.local.repository.client_article_prix

import com.asmtunis.procaisseinventory.articles.data.article.domaine.ClientArticlePrix
import kotlinx.coroutines.flow.Flow


interface ClientArticlePrixLocalRepository {

    fun upsert(value: ClientArticlePrix)

    fun upsertAll(value: List<ClientArticlePrix>)


    fun deleteAll()

    fun getAll(): Flow<List<ClientArticlePrix>>

}

