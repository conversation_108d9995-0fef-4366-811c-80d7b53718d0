kotlin version: 2.1.20
error message: org.jetbrains.kotlin.backend.common.BackendException: Backend Internal error: Exception during IR lowering
File being compiled: D:/Project/MergeProcCaisseProInventory_Dev/app/src/main/java/com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel.kt
The root cause java.lang.RuntimeException was thrown at: org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:48)
	at org.jetbrains.kotlin.backend.common.CodegenUtil.reportBackendException(CodegenUtil.kt:107)
	at org.jetbrains.kotlin.backend.common.CodegenUtil.reportBackendException$default(CodegenUtil.kt:90)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invokeSequential(performByIrFile.kt:54)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invoke(performByIrFile.kt:41)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invoke(performByIrFile.kt:27)
	at org.jetbrains.kotlin.config.phaser.CompilerPhaseKt.invokeToplevel(CompilerPhase.kt:62)
	at org.jetbrains.kotlin.backend.jvm.JvmIrCodegenFactory.invokeCodegen(JvmIrCodegenFactory.kt:371)
	at org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.runCodegen$cli(KotlinToJVMBytecodeCompiler.kt:413)
	at org.jetbrains.kotlin.cli.pipeline.jvm.JvmBackendPipelinePhase.executePhase(JvmBackendPipelinePhase.kt:98)
	at org.jetbrains.kotlin.cli.pipeline.jvm.JvmBackendPipelinePhase.executePhase(JvmBackendPipelinePhase.kt:30)
	at org.jetbrains.kotlin.cli.pipeline.PipelinePhase.phaseBody(PipelinePhase.kt:68)
	at org.jetbrains.kotlin.cli.pipeline.PipelinePhase.phaseBody(PipelinePhase.kt:58)
	at org.jetbrains.kotlin.config.phaser.SimpleNamedCompilerPhase.phaseBody(CompilerPhase.kt:215)
	at org.jetbrains.kotlin.config.phaser.NamedCompilerPhase.invoke(CompilerPhase.kt:111)
	at org.jetbrains.kotlin.backend.common.phaser.CompositePhase.invoke(PhaseBuilders.kt:28)
	at org.jetbrains.kotlin.config.phaser.CompilerPhaseKt.invokeToplevel(CompilerPhase.kt:62)
	at org.jetbrains.kotlin.cli.pipeline.AbstractCliPipeline.runPhasedPipeline(AbstractCliPipeline.kt:106)
	at org.jetbrains.kotlin.cli.pipeline.AbstractCliPipeline.execute(AbstractCliPipeline.kt:65)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecutePhased(K2JVMCompiler.kt:61)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecutePhased(K2JVMCompiler.kt:36)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:80)
	at org.jetbrains.kotlin.cli.common.CLICompiler.exec(CLICompiler.kt:337)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:466)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:75)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:514)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:431)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileNonIncrementally(IncrementalCompilerRunner.kt:310)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:137)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:678)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1805)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.RuntimeException: Exception while generating code for:
FUN name:facturerBL visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, ticketWithFactureAndPayments:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments) returnType:kotlin.Unit
  $this: VALUE_PARAMETER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel
  VALUE_PARAMETER name:ticketWithFactureAndPayments index:0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
  BLOCK_BODY
    CALL 'private final fun <set-ticketsWithLinesAndPaymentsNotSync> (<set-?>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
      $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
      <set-?>: CALL 'public final fun emptyList <T> (): kotlin.collections.List<T of kotlin.collections.CollectionsKt.emptyList> declared in kotlin.collections.CollectionsKt' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> origin=null
        <T>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
    CALL 'private final fun <set-ticketsWithLinesAndPaymentsNotSync> (<set-?>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
      $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
      <set-?>: CALL 'public final fun listOf <T> (element: T of kotlin.collections.CollectionsKt.listOf): kotlin.collections.List<T of kotlin.collections.CollectionsKt.listOf> declared in kotlin.collections.CollectionsKt' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> origin=null
        <T>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
        element: GET_VAR 'ticketWithFactureAndPayments: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments origin=null
    CALL 'private final fun setFactureTimbreAndRevImp (): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=null
      $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
    COMPOSITE type=kotlin.Unit origin=null
      CALL 'public final fun launch$default (context: kotlin.coroutines.CoroutineContext?, start: kotlinx.coroutines.CoroutineStart?, block: @[ExtensionFunctionType] kotlin.coroutines.SuspendFunction1<kotlinx.coroutines.CoroutineScope, kotlin.Unit>, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlinx.coroutines.Job declared in kotlinx.coroutines.BuildersKt' type=kotlinx.coroutines.Job origin=DEFAULT_DISPATCH_CALL
        $receiver: CALL 'public final fun <get-viewModelScope> (): kotlinx.coroutines.CoroutineScope declared in androidx.lifecycle.ViewModelKt' type=kotlinx.coroutines.CoroutineScope origin=GET_PROPERTY
          $receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
        context: GET_FIELD 'FIELD PROPERTY_BACKING_FIELD name:dispatcherIO type:kotlinx.coroutines.CoroutineDispatcher visibility:private [final]' type=kotlinx.coroutines.CoroutineDispatcher origin=null
          receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
        start: COMPOSITE type=kotlinx.coroutines.CoroutineStart? origin=DEFAULT_VALUE
          CONST Null type=kotlinx.coroutines.CoroutineStart? value=null
        block: BLOCK type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
          CLASS SUSPEND_LAMBDA CLASS name:<no name provided> modality:FINAL visibility:public/*package*/ superTypes:[kotlin.coroutines.jvm.internal.SuspendLambda; kotlin.jvm.functions.Function2<kotlinx.coroutines.CoroutineScope, kotlin.coroutines.Continuation<kotlin.Unit>?, kotlin.Any?>]
            $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>
            FIELD name:label type:kotlin.Int visibility:public/*package*/
            FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:private
            CONSTRUCTOR SUSPEND_LAMBDA visibility:public/*package*/ <> (this$0:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion:kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>>?) returnType:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> [primary]
              VALUE_PARAMETER BOUND_RECEIVER_PARAMETER name:this$0 index:0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel
              VALUE_PARAMETER name:$completion index:1 type:kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>>?
              BLOCK_BODY
                SET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlin.Unit origin=INITIALIZER_OF_FIELD_FOR_CAPTURED_VALUE
                  receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                  value: GET_VAR 'this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.<init>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                DELEGATING_CONSTRUCTOR_CALL 'public constructor <init> (arity: kotlin.Int, $completion: kotlin.coroutines.Continuation<kotlin.Any?>?) declared in kotlin.coroutines.jvm.internal.SuspendLambda'
                  arity: CONST Int type=kotlin.Int value=2
                  $completion: GET_VAR '$completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>>? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.<init>' type=kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>>? origin=null
                BLOCK type=kotlin.Unit origin=null
            FUN name:invokeSuspend visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>, $result:kotlin.Result<kotlin.Any?>) returnType:kotlin.Any?
              overridden:
                protected abstract fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in kotlin.coroutines.jvm.internal.SuspendLambda
              $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>
              VALUE_PARAMETER name:$result index:0 type:kotlin.Result<kotlin.Any?>
              BLOCK_BODY
                VAR SUSPEND_LAMBDA_PARAMETER name:$this$launch type:kotlinx.coroutines.CoroutineScope [val]
                  BLOCK type=kotlinx.coroutines.CoroutineScope origin=null
                    GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:private declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlinx.coroutines.CoroutineScope origin=null
                      receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                VAR name:baseConfigObj type:com.asmtunis.procaisseinventory.core.model.GenericObject [val]
                  CONSTRUCTOR_CALL 'public constructor <init> (connexion: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig, data: kotlinx.serialization.json.JsonElement?) [primary] declared in com.asmtunis.procaisseinventory.core.model.GenericObject' type=com.asmtunis.procaisseinventory.core.model.GenericObject origin=null
                    connexion: BLOCK type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig origin=FOLDED_ELVIS
                      WHEN type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig origin=FOLDED_ELVIS
                        BRANCH
                          if: CALL 'public final fun ANDAND (arg0: kotlin.Boolean, arg1: kotlin.Boolean): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                            arg0: COMPOSITE type=kotlin.Boolean origin=null
                              VAR IR_TEMPORARY_VARIABLE name:tmp0_safe_receiver type:kotlin.String? [val]
                                CALL 'public final fun first <T> ($completion: kotlin.coroutines.Continuation<T of kotlinx.coroutines.flow.FlowKt.first>): kotlin.Any? [suspend] declared in kotlinx.coroutines.flow.FlowKt' type=kotlin.String? origin=null
                                  <T>: kotlin.String?
                                  $receiver: CALL 'public open fun getString$default ($this: com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository, key: kotlin.String, default: kotlin.String?, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlinx.coroutines.flow.Flow<kotlin.String?> declared in com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository.DefaultImpls' type=kotlinx.coroutines.flow.Flow<kotlin.String?> origin=DEFAULT_DISPATCH_CALL
                                    $this: CALL 'public final fun <get-dataStore> (): com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository declared in com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb' type=com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository origin=GET_PROPERTY
                                      $this: CALL 'public final fun access$getProCaisseLocalDb$p ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel): com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb origin=null
                                        $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                          receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                                    key: CONST String type=kotlin.String value="Seleceted_BASE_CONFIG"
                                    default: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                                      CONST Null type=kotlin.String? value=null
                                    $mask0: CONST Int type=kotlin.Int value=2
                                    $handler: CONST Null type=kotlin.Any? value=null
                                  $completion: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                              CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                $this: CALL 'public final fun EQEQ (arg0: kotlin.Any?, arg1: kotlin.Any?): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                                  arg0: GET_VAR 'val tmp0_safe_receiver: kotlin.String? [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=kotlin.String? origin=null
                                  arg1: CONST Null type=kotlin.Nothing? value=null
                            arg1: COMPOSITE type=kotlin.Boolean origin=null
                              VAR IR_TEMPORARY_VARIABLE name:tmp1_elvis_lhs type:com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? [val]
                                CALL 'public final fun let <T, R> (block: kotlin.Function1<T of kotlin.StandardKt.let, R of kotlin.StandardKt.let>): R of kotlin.StandardKt.let [inline] declared in kotlin.StandardKt' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? origin=null
                                  <T>: kotlin.String
                                  <R>: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig?
                                  $receiver: GET_VAR 'val tmp0_safe_receiver: kotlin.String? [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=kotlin.String? origin=null
                                  block: BLOCK type=kotlin.Function1<kotlin.String, com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig?> origin=LAMBDA
                                    COMPOSITE type=kotlin.Unit origin=null
                                    FUNCTION_REFERENCE 'private final fun invokeSuspend$lambda$0 (it: kotlin.String): com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlin.Function1<kotlin.String, com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig?> origin=INLINE_LAMBDA reflectionTarget=null
                              CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                $this: CALL 'public final fun EQEQ (arg0: kotlin.Any?, arg1: kotlin.Any?): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                                  arg0: GET_VAR 'val tmp1_elvis_lhs: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? origin=null
                                  arg1: CONST Null type=kotlin.Nothing? value=null
                          then: GET_VAR 'val tmp1_elvis_lhs: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? origin=null
                        BRANCH
                          if: CONST Boolean type=kotlin.Boolean value=true
                          then: CONSTRUCTOR_CALL 'public constructor <init> (id_base_config: kotlin.String?, key_base: kotlin.String?, dbName: kotlin.String?, dbIpAddress: kotlin.String?, adresse_ip: kotlin.String?, port: kotlin.String?, username: kotlin.String?, password: kotlin.String?, designation_base: kotlin.String?, produit: kotlin.String?, id_entreprise: kotlin.String?, date_creation: kotlin.String?, licences: kotlin.collections.List<com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence>?, $mask0: kotlin.Int, $marker: kotlin.jvm.internal.DefaultConstructorMarker?) declared in com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig origin=DEFAULT_DISPATCH_CALL
                            id_base_config: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            key_base: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            dbName: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            dbIpAddress: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            adresse_ip: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            port: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            username: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            password: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            designation_base: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            produit: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            id_entreprise: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            date_creation: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.String? value=null
                            licences: COMPOSITE type=kotlin.collections.List<com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence>? origin=DEFAULT_VALUE
                              CONST Null type=kotlin.collections.List<com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence>? value=null
                            $mask0: CONST Int type=kotlin.Int value=8191
                            $marker: CONST Null type=kotlin.jvm.internal.DefaultConstructorMarker? value=null
                    data: CALL 'public final fun encodeToJsonElement <T> (value: T of kotlinx.serialization.json.JsonKt.encodeToJsonElement): kotlinx.serialization.json.JsonElement [inline] declared in kotlinx.serialization.json.JsonKt' type=kotlinx.serialization.json.JsonElement origin=null
                      <T>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments>
                      $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Default type:kotlinx.serialization.json.Json.Default visibility:public [final,static] declared in kotlinx.serialization.json.Json' type=kotlinx.serialization.json.Json.Default origin=null
                      value: CALL 'public final fun <get-ticketsWithLinesAndPaymentsNotSync> (): kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> origin=GET_PROPERTY
                        $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                          receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                COMPOSITE type=kotlin.Unit origin=null
                  CALL 'public final fun launchIn <T> (scope: kotlinx.coroutines.CoroutineScope): kotlinx.coroutines.Job declared in kotlinx.coroutines.flow.FlowKt' type=kotlinx.coroutines.Job origin=null
                    <T>: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                    $receiver: CALL 'public final fun flowOn <T> (context: kotlin.coroutines.CoroutineContext): kotlinx.coroutines.flow.Flow<T of kotlinx.coroutines.flow.FlowKt.flowOn> declared in kotlinx.coroutines.flow.FlowKt' type=kotlinx.coroutines.flow.Flow<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>> origin=null
                      <T>: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                      $receiver: CALL 'public final fun onEach <T> (action: kotlin.coroutines.SuspendFunction1<T of kotlinx.coroutines.flow.FlowKt.onEach, kotlin.Unit>): kotlinx.coroutines.flow.Flow<T of kotlinx.coroutines.flow.FlowKt.onEach> declared in kotlinx.coroutines.flow.FlowKt' type=kotlinx.coroutines.flow.Flow<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>> origin=null
                        <T>: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                        $receiver: CALL 'public abstract fun addBatchFactureWithLines (baseConfig: kotlin.String, $completion: kotlin.coroutines.Continuation<kotlinx.coroutines.flow.Flow<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>>>): kotlin.Any? [suspend] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApi' type=kotlinx.coroutines.flow.Flow<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>> origin=null
                          $this: CALL 'public final fun <get-ticket> (): com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApi declared in com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApi origin=GET_PROPERTY
                            $this: CALL 'public final fun access$getProCaisseRemote$p ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel): com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote origin=null
                              $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                          baseConfig: CALL 'public final fun encodeToString <T> (value: T of kotlinx.serialization.json.Json.encodeToString): kotlin.String [inline] declared in kotlinx.serialization.json.Json' type=kotlin.String origin=null
                            <T>: com.asmtunis.procaisseinventory.core.model.GenericObject
                            $this: TYPE_OP type=kotlinx.serialization.json.Json origin=IMPLICIT_CAST typeOperand=kotlinx.serialization.json.Json
                              GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Default type:kotlinx.serialization.json.Json.Default visibility:public [final,static] declared in kotlinx.serialization.json.Json' type=kotlinx.serialization.json.Json.Default origin=null
                            value: GET_VAR 'val baseConfigObj: com.asmtunis.procaisseinventory.core.model.GenericObject [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.GenericObject origin=null
                          $completion: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                        action: BLOCK type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                          CLASS SUSPEND_LAMBDA CLASS name:<no name provided> modality:FINAL visibility:public/*package*/ superTypes:[kotlin.coroutines.jvm.internal.SuspendLambda; kotlin.jvm.functions.Function2<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>, kotlin.coroutines.Continuation<kotlin.Unit>?, kotlin.Any?>]
                            $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                            FIELD name:label type:kotlin.Int visibility:public/*package*/
                            FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:public/*package*/
                            CONSTRUCTOR SUSPEND_LAMBDA visibility:public/*package*/ <> (this$0:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion:kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>?) returnType:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> [primary]
                              VALUE_PARAMETER BOUND_RECEIVER_PARAMETER name:this$0 index:0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel
                              VALUE_PARAMETER name:$completion index:1 type:kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>?
                              BLOCK_BODY
                                SET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.Unit origin=INITIALIZER_OF_FIELD_FOR_CAPTURED_VALUE
                                  receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                  value: GET_VAR 'this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.<init>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                DELEGATING_CONSTRUCTOR_CALL 'public constructor <init> (arity: kotlin.Int, $completion: kotlin.coroutines.Continuation<kotlin.Any?>?) declared in kotlin.coroutines.jvm.internal.SuspendLambda'
                                  arity: CONST Int type=kotlin.Int value=2
                                  $completion: GET_VAR '$completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.<init>' type=kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>? origin=null
                                BLOCK type=kotlin.Unit origin=null
                            FUN name:invokeSuspend visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>, $result:kotlin.Result<kotlin.Any?>) returnType:kotlin.Any?
                              overridden:
                                protected abstract fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in kotlin.coroutines.jvm.internal.SuspendLambda
                              $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                              VALUE_PARAMETER name:$result index:0 type:kotlin.Result<kotlin.Any?>
                              BLOCK_BODY
                                VAR SUSPEND_LAMBDA_PARAMETER name:result type:com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val]
                                  BLOCK type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                    GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:public/*package*/ declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                      receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                BLOCK type=kotlin.Unit origin=WHEN
                                  VAR IR_TEMPORARY_VARIABLE name:tmp0_subject type:com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val]
                                    GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                  WHEN type=kotlin.Unit origin=WHEN
                                    BRANCH
                                      if: TYPE_OP type=kotlin.Boolean origin=INSTANCEOF typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                        GET_VAR 'val tmp0_subject: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                      then: BLOCK type=kotlin.Unit origin=null
                                        CALL 'public final fun access$setResponseFactureTicket ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                                          $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                            receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                          <set-?>: CONSTRUCTOR_CALL 'public constructor <init> (data: T of com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState?, loading: kotlin.Boolean, error: kotlin.String?, message: kotlin.String?, $mask0: kotlin.Int, $marker: kotlin.jvm.internal.DefaultConstructorMarker?) declared in com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState' type=com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=DEFAULT_DISPATCH_CALL
                                            <class: T>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>
                                            data: CALL 'public final fun <get-data> (): T of com.asmtunis.procaisseinventory.core.model.DataResult.Success? [fake_override] declared in com.asmtunis.procaisseinventory.core.model.DataResult.Success' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>? origin=GET_PROPERTY
                                              $this: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                                GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                            loading: CONST Boolean type=kotlin.Boolean value=false
                                            error: CONST Null type=kotlin.Nothing? value=null
                                            message: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                                              CONST Null type=kotlin.String? value=null
                                            $mask0: CONST Int type=kotlin.Int value=8
                                            $marker: CONST Null type=kotlin.jvm.internal.DefaultConstructorMarker? value=null
                                        CALL 'public final fun access$setTicketsWithLinesAndPaymentsNotSync ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                                          $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                            receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                          <set-?>: CALL 'public final fun emptyList <T> (): kotlin.collections.List<T of kotlin.collections.CollectionsKt.emptyList> declared in kotlin.collections.CollectionsKt' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> origin=null
                                            <T>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
                                        BLOCK type=kotlin.Unit origin=FOR_LOOP
                                          COMPOSITE type=kotlin.Unit origin=null
                                            VAR FOR_LOOP_VARIABLE name:i type:kotlin.Int [var]
                                              CONST Int type=kotlin.Int value=0
                                            VAR IR_TEMPORARY_VARIABLE name:last type:kotlin.Int [val]
                                              CALL 'public abstract fun <get-size> (): kotlin.Int declared in kotlin.collections.Collection' type=kotlin.Int origin=null
                                                $this: CALL 'public final fun CHECK_NOT_NULL <T0> (arg0: T0 of kotlin.internal.ir.CHECK_NOT_NULL?): {T0 of kotlin.internal.ir.CHECK_NOT_NULL & Any} declared in kotlin.internal.ir' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate> origin=EXCLEXCL
                                                  <T0>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>
                                                  arg0: CALL 'public final fun <get-data> (): T of com.asmtunis.procaisseinventory.core.model.DataResult.Success? [fake_override] declared in com.asmtunis.procaisseinventory.core.model.DataResult.Success' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>? origin=GET_PROPERTY
                                                    $this: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                                      GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                          DO_WHILE label=SyncBonLivraisonViewModel$facturerBL$<no name provided>$invokeSuspend$<no name provided>$invokeSuspend2 origin=DO_WHILE_COUNTER_LOOP
                                            body: COMPOSITE type=kotlin.Unit origin=FOR_LOOP_INNER_WHILE
                                              COMPOSITE type=kotlin.Unit origin=FOR_LOOP_NEXT
                                                WHEN type=kotlin.Unit origin=null
                                                  BRANCH
                                                    if: CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                                      $this: CALL 'public final fun less (arg0: kotlin.Int, arg1: kotlin.Int): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                                                        arg0: GET_VAR 'var i: kotlin.Int [var] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=kotlin.Int origin=null
                                                        arg1: GET_VAR 'val last: kotlin.Int [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=kotlin.Int origin=null
                                                    then: BREAK label=null loop.label=SyncBonLivraisonViewModel$facturerBL$<no name provided>$invokeSuspend$<no name provided>$invokeSuspend2
                                              BLOCK type=kotlin.Unit origin=null
                                                VAR name:ticketUpdate type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val]
                                                  CALL 'public abstract fun get (index: kotlin.Int): E of kotlin.collections.List [operator] declared in kotlin.collections.List' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=GET_ARRAY_ELEMENT
                                                    $this: CALL 'public final fun <get-data> (): T of com.asmtunis.procaisseinventory.core.model.DataResult.Success? [fake_override] declared in com.asmtunis.procaisseinventory.core.model.DataResult.Success' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>? origin=GET_PROPERTY
                                                      $this: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                                        GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                                    index: GET_VAR 'var i: kotlin.Int [var] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=kotlin.Int origin=null
                                                WHEN type=kotlin.Unit origin=IF
                                                  BRANCH
                                                    if: CALL 'public final fun ANDAND (arg0: kotlin.Boolean, arg1: kotlin.Boolean): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                                                      arg0: CALL 'public final fun ANDAND (arg0: kotlin.Boolean, arg1: kotlin.Boolean): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                                                        arg0: CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                                          $this: CALL 'public final fun equals$default (other: kotlin.String?, ignoreCase: kotlin.Boolean, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Boolean declared in kotlin.text.StringsKt' type=kotlin.Boolean origin=DEFAULT_DISPATCH_CALL
                                                            $receiver: CALL 'public final fun <get-code> (): kotlin.String? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate' type=kotlin.String? origin=GET_PROPERTY
                                                              $this: GET_VAR 'val ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=null
                                                            other: CONST String type=kotlin.String value="10200"
                                                            ignoreCase: COMPOSITE type=kotlin.Boolean origin=DEFAULT_VALUE
                                                              CONST Boolean type=kotlin.Boolean value=false
                                                            $mask0: CONST Int type=kotlin.Int value=2
                                                            $handler: CONST Null type=kotlin.Any? value=null
                                                        arg1: CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                                          $this: CALL 'public final fun equals$default (other: kotlin.String?, ignoreCase: kotlin.Boolean, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Boolean declared in kotlin.text.StringsKt' type=kotlin.Boolean origin=DEFAULT_DISPATCH_CALL
                                                            $receiver: CALL 'public final fun <get-code> (): kotlin.String? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate' type=kotlin.String? origin=GET_PROPERTY
                                                              $this: GET_VAR 'val ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=null
                                                            other: CONST String type=kotlin.String value="10201"
                                                            ignoreCase: COMPOSITE type=kotlin.Boolean origin=DEFAULT_VALUE
                                                              CONST Boolean type=kotlin.Boolean value=false
                                                            $mask0: CONST Int type=kotlin.Int value=2
                                                            $handler: CONST Null type=kotlin.Any? value=null
                                                      arg1: CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                                        $this: CALL 'public final fun equals$default (other: kotlin.String?, ignoreCase: kotlin.Boolean, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Boolean declared in kotlin.text.StringsKt' type=kotlin.Boolean origin=DEFAULT_DISPATCH_CALL
                                                          $receiver: CALL 'public final fun <get-code> (): kotlin.String? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate' type=kotlin.String? origin=GET_PROPERTY
                                                            $this: GET_VAR 'val ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=null
                                                          other: CONST String type=kotlin.String value="10304"
                                                          ignoreCase: COMPOSITE type=kotlin.Boolean origin=DEFAULT_VALUE
                                                            CONST Boolean type=kotlin.Boolean value=false
                                                          $mask0: CONST Int type=kotlin.Int value=2
                                                          $handler: CONST Null type=kotlin.Any? value=null
                                                    then: BLOCK type=kotlin.Unit origin=null
                                                      RETURN type=kotlin.Nothing from='public final fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>'
                                                        GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:kotlin.Unit visibility:public [final,static] declared in kotlin.Unit' type=kotlin.Unit origin=null
                                                CALL 'public final fun access$updateLocalData ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate, insertFacture: kotlin.Boolean): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=null
                                                  $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                                    receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                                  ticketUpdate: GET_VAR 'val ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=null
                                                  insertFacture: CONST Boolean type=kotlin.Boolean value=true
                                            condition: COMPOSITE type=kotlin.Boolean origin=null
                                              CALL 'public final fun <int-prefix-incr-decr> (value: kotlin.Int, delta: kotlin.Int): kotlin.Int declared in kotlin.jvm.internal' type=kotlin.Int origin=null
                                                value: GET_VAR 'var i: kotlin.Int [var] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=kotlin.Int origin=null
                                                delta: CONST Int type=kotlin.Int value=1
                                              CONST Boolean type=kotlin.Boolean value=true
                                    BRANCH
                                      if: TYPE_OP type=kotlin.Boolean origin=INSTANCEOF typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Loading<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                        GET_VAR 'val tmp0_subject: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                      then: BLOCK type=kotlin.Unit origin=null
                                        CALL 'public final fun access$setResponseFactureTicket ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                                          $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                            receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                          <set-?>: CONSTRUCTOR_CALL 'public constructor <init> (data: T of com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState?, loading: kotlin.Boolean, error: kotlin.String?, message: kotlin.String?, $mask0: kotlin.Int, $marker: kotlin.jvm.internal.DefaultConstructorMarker?) declared in com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState' type=com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=DEFAULT_DISPATCH_CALL
                                            <class: T>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>
                                            data: CONST Null type=kotlin.Nothing? value=null
                                            loading: CONST Boolean type=kotlin.Boolean value=true
                                            error: CONST Null type=kotlin.Nothing? value=null
                                            message: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                                              CONST Null type=kotlin.String? value=null
                                            $mask0: CONST Int type=kotlin.Int value=8
                                            $marker: CONST Null type=kotlin.jvm.internal.DefaultConstructorMarker? value=null
                                    BRANCH
                                      if: TYPE_OP type=kotlin.Boolean origin=INSTANCEOF typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Error<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                        GET_VAR 'val tmp0_subject: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                      then: BLOCK type=kotlin.Unit origin=null
                                        CALL 'public final fun access$setTicketsWithLinesAndPaymentsNotSync ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                                          $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                            receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                          <set-?>: CALL 'public final fun emptyList <T> (): kotlin.collections.List<T of kotlin.collections.CollectionsKt.emptyList> declared in kotlin.collections.CollectionsKt' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> origin=null
                                            <T>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
                                        CALL 'public final fun access$setResponseFactureTicket ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                                          $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                            receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                          <set-?>: CONSTRUCTOR_CALL 'public constructor <init> (data: T of com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState?, loading: kotlin.Boolean, error: kotlin.String?, message: kotlin.String?, $mask0: kotlin.Int, $marker: kotlin.jvm.internal.DefaultConstructorMarker?) declared in com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState' type=com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=DEFAULT_DISPATCH_CALL
                                            <class: T>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>
                                            data: CONST Null type=kotlin.Nothing? value=null
                                            loading: CONST Boolean type=kotlin.Boolean value=false
                                            error: CALL 'public final fun <get-message> (): kotlin.String? [fake_override] declared in com.asmtunis.procaisseinventory.core.model.DataResult.Error' type=kotlin.String? origin=GET_PROPERTY
                                              $this: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult.Error<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Error<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                                GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                            message: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                                              CONST Null type=kotlin.String? value=null
                                            $mask0: CONST Int type=kotlin.Int value=8
                                            $marker: CONST Null type=kotlin.jvm.internal.DefaultConstructorMarker? value=null
                                    BRANCH
                                      if: CONST Boolean type=kotlin.Boolean value=true
                                      then: BLOCK type=kotlin.Nothing origin=null
                                        CALL 'public final fun noWhenBranchMatchedException (): kotlin.Nothing declared in kotlin.internal.ir' type=kotlin.Nothing origin=null
                                        CALL 'public final fun throwKotlinNothingValueException (): kotlin.Nothing declared in kotlin.jvm.internal.Intrinsics' type=kotlin.Nothing origin=null
                            FUN name:create visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>, value:kotlin.Any?, $completion:kotlin.coroutines.Continuation<kotlin.Nothing>) returnType:kotlin.coroutines.Continuation<kotlin.Unit>
                              overridden:
                                public open fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in kotlin.coroutines.jvm.internal.SuspendLambda
                              $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                              VALUE_PARAMETER name:value index:0 type:kotlin.Any?
                              VALUE_PARAMETER name:$completion index:1 type:kotlin.coroutines.Continuation<kotlin.Nothing>
                              BLOCK_BODY
                                VAR IR_TEMPORARY_VARIABLE name:tmp0_result type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> [val]
                                  CONSTRUCTOR_CALL 'public/*package*/ constructor <init> (this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>?) [primary] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                    this$0: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                      receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                    $completion: GET_VAR '$completion: kotlin.coroutines.Continuation<kotlin.Nothing> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=kotlin.coroutines.Continuation<kotlin.Nothing> origin=null
                                SET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:public/*package*/ declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.Unit origin=null
                                  receiver: GET_VAR 'val tmp0_result: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                  value: GET_VAR 'value: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=kotlin.Any? origin=null
                                RETURN type=kotlin.Nothing from='public final fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>'
                                  GET_VAR 'val tmp0_result: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                            FUN name:invoke visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>, p1:com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>, p2:kotlin.coroutines.Continuation<kotlin.Unit>?) returnType:kotlin.Any?
                              overridden:
                                public abstract fun invoke (p1: P1 of kotlin.jvm.functions.Function2, p2: P2 of kotlin.jvm.functions.Function2): R of kotlin.jvm.functions.Function2 declared in kotlin.jvm.functions.Function2
                              $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                              VALUE_PARAMETER name:p1 index:0 type:com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                              VALUE_PARAMETER name:p2 index:1 type:kotlin.coroutines.Continuation<kotlin.Unit>?
                              BLOCK_BODY
                                RETURN type=kotlin.Nothing from='public final fun invoke (p1: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>, p2: kotlin.coroutines.Continuation<kotlin.Unit>?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>'
                                  CALL 'public final fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.Any? origin=null
                                    $this: TYPE_OP type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                                      CALL 'public final fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.coroutines.Continuation<kotlin.Unit> origin=null
                                        $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                        value: GET_VAR 'p1: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                        $completion: GET_VAR 'p2: kotlin.coroutines.Continuation<kotlin.Unit>? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=kotlin.coroutines.Continuation<kotlin.Unit>? origin=null
                                    $result: CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=kotlin.Result<kotlin.Any?> origin=null
                                      <T>: kotlin.Any?
                                      <R>: kotlin.Result<kotlin.Any?>
                                      v: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:kotlin.Unit visibility:public [final,static] declared in kotlin.Unit' type=kotlin.Unit origin=null
                            FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final]
                            FUN BRIDGE name:invoke visibility:public modality:OPEN <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>, p1:kotlin.Any?, p2:kotlin.Any?) returnType:kotlin.Any?
                              $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                              VALUE_PARAMETER BRIDGE name:p1 index:0 type:kotlin.Any?
                              VALUE_PARAMETER BRIDGE name:p2 index:1 type:kotlin.Any?
                              EXPRESSION_BODY
                                RETURN type=kotlin.Nothing from='public open fun invoke (p1: kotlin.Any?, p2: kotlin.Any?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>'
                                  CALL 'public final fun invoke (p1: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>, p2: kotlin.coroutines.Continuation<kotlin.Unit>?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.Any? origin=BRIDGE_DELEGATION
                                    $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                    p1: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult<*> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult<*>
                                      GET_VAR 'p1: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=kotlin.Any? origin=null
                                    p2: TYPE_OP type=kotlin.coroutines.Continuation<*> origin=IMPLICIT_CAST typeOperand=kotlin.coroutines.Continuation<*>
                                      GET_VAR 'p2: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=kotlin.Any? origin=null
                          CONSTRUCTOR_CALL 'public/*package*/ constructor <init> (this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>?) [primary] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                            this$0: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                              receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                            $completion: CONST Null type=kotlin.Nothing? value=null
                      context: CALL 'public final fun access$getDispatcherIO$p ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel): kotlinx.coroutines.CoroutineDispatcher declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlinx.coroutines.CoroutineDispatcher origin=null
                        $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                          receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                    scope: GET_VAR 'val $this$launch: kotlinx.coroutines.CoroutineScope [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=kotlinx.coroutines.CoroutineScope origin=null
                  COMPOSITE type=kotlin.Unit origin=null
            FUN name:create visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>, value:kotlin.Any?, $completion:kotlin.coroutines.Continuation<kotlin.Nothing>) returnType:kotlin.coroutines.Continuation<kotlin.Unit>
              overridden:
                public open fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in kotlin.coroutines.jvm.internal.SuspendLambda
              $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>
              VALUE_PARAMETER name:value index:0 type:kotlin.Any?
              VALUE_PARAMETER name:$completion index:1 type:kotlin.coroutines.Continuation<kotlin.Nothing>
              BLOCK_BODY
                VAR IR_TEMPORARY_VARIABLE name:tmp0_result type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> [val]
                  CONSTRUCTOR_CALL 'public/*package*/ constructor <init> (this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>>?) [primary] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                    this$0: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                      receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                    $completion: GET_VAR '$completion: kotlin.coroutines.Continuation<kotlin.Nothing> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.create' type=kotlin.coroutines.Continuation<kotlin.Nothing> origin=null
                SET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:private declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlin.Unit origin=null
                  receiver: GET_VAR 'val tmp0_result: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                  value: GET_VAR 'value: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.create' type=kotlin.Any? origin=null
                RETURN type=kotlin.Nothing from='public final fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>'
                  GET_VAR 'val tmp0_result: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
            FUN name:invoke visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>, p1:kotlinx.coroutines.CoroutineScope, p2:kotlin.coroutines.Continuation<kotlin.Unit>?) returnType:kotlin.Any?
              overridden:
                public abstract fun invoke (p1: P1 of kotlin.jvm.functions.Function2, p2: P2 of kotlin.jvm.functions.Function2): R of kotlin.jvm.functions.Function2 declared in kotlin.jvm.functions.Function2
              $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>
              VALUE_PARAMETER name:p1 index:0 type:kotlinx.coroutines.CoroutineScope
              VALUE_PARAMETER name:p2 index:1 type:kotlin.coroutines.Continuation<kotlin.Unit>?
              BLOCK_BODY
                RETURN type=kotlin.Nothing from='public final fun invoke (p1: kotlinx.coroutines.CoroutineScope, p2: kotlin.coroutines.Continuation<kotlin.Unit>?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>'
                  CALL 'public final fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlin.Any? origin=null
                    $this: TYPE_OP type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>
                      CALL 'public final fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlin.coroutines.Continuation<kotlin.Unit> origin=null
                        $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invoke' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                        value: GET_VAR 'p1: kotlinx.coroutines.CoroutineScope declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invoke' type=kotlinx.coroutines.CoroutineScope origin=null
                        $completion: GET_VAR 'p2: kotlin.coroutines.Continuation<kotlin.Unit>? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invoke' type=kotlin.coroutines.Continuation<kotlin.Unit>? origin=null
                    $result: CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=kotlin.Result<kotlin.Any?> origin=null
                      <T>: kotlin.Any?
                      <R>: kotlin.Result<kotlin.Any?>
                      v: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:kotlin.Unit visibility:public [final,static] declared in kotlin.Unit' type=kotlin.Unit origin=null
            FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final]
            FUN INLINE_LAMBDA name:invokeSuspend$lambda$0 visibility:private modality:FINAL <> (it:kotlin.String) returnType:com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig?
              VALUE_PARAMETER name:it index:0 type:kotlin.String
              BLOCK_BODY
                VAR name:$i$a$-let-SyncBonLivraisonViewModel$facturerBL$1$baseConfigObj$1 type:kotlin.Int [val]
                  CONST Int type=kotlin.Int value=0
                RETURN type=kotlin.Nothing from='private final fun invokeSuspend$lambda$0 (it: kotlin.String): com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>'
                  CALL 'public final fun decodeFromString <T> (string: kotlin.String): T of kotlinx.serialization.json.Json.decodeFromString [inline] declared in kotlinx.serialization.json.Json' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? origin=null
                    <T>: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig?
                    $this: TYPE_OP type=kotlinx.serialization.json.Json origin=IMPLICIT_CAST typeOperand=kotlinx.serialization.json.Json
                      GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Default type:kotlinx.serialization.json.Json.Default visibility:public [final,static] declared in kotlinx.serialization.json.Json' type=kotlinx.serialization.json.Json.Default origin=null
                    string: GET_VAR 'it: kotlin.String declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend$lambda$0' type=kotlin.String origin=null
            FUN BRIDGE name:invoke visibility:public modality:OPEN <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>, p1:kotlin.Any?, p2:kotlin.Any?) returnType:kotlin.Any?
              $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>
              VALUE_PARAMETER BRIDGE name:p1 index:0 type:kotlin.Any?
              VALUE_PARAMETER BRIDGE name:p2 index:1 type:kotlin.Any?
              EXPRESSION_BODY
                RETURN type=kotlin.Nothing from='public open fun invoke (p1: kotlin.Any?, p2: kotlin.Any?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>'
                  CALL 'public final fun invoke (p1: kotlinx.coroutines.CoroutineScope, p2: kotlin.coroutines.Continuation<kotlin.Unit>?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlin.Any? origin=BRIDGE_DELEGATION
                    $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invoke' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                    p1: TYPE_OP type=kotlinx.coroutines.CoroutineScope origin=IMPLICIT_CAST typeOperand=kotlinx.coroutines.CoroutineScope
                      GET_VAR 'p1: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invoke' type=kotlin.Any? origin=null
                    p2: TYPE_OP type=kotlin.coroutines.Continuation<*> origin=IMPLICIT_CAST typeOperand=kotlin.coroutines.Continuation<*>
                      GET_VAR 'p2: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invoke' type=kotlin.Any? origin=null
          CONSTRUCTOR_CALL 'public/*package*/ constructor <init> (this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>>?) [primary] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
            this$0: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
            $completion: CONST Null type=kotlin.Nothing? value=null
        $mask0: CONST Int type=kotlin.Int value=2
        $handler: CONST Null type=kotlin.Any? value=null
      COMPOSITE type=kotlin.Unit origin=null

	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:48)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate$default(FunctionCodegen.kt:41)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethodNode(ClassCodegen.kt:405)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethod(ClassCodegen.kt:422)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generate(ClassCodegen.kt:167)
	at org.jetbrains.kotlin.backend.jvm.JvmIrCodegenFactory.generateFile$lambda$18(JvmIrCodegenFactory.kt:398)
	at org.jetbrains.kotlin.backend.common.phaser.PhaseBuildersKt$createSimpleNamedCompilerPhase$1.phaseBody(PhaseBuilders.kt:68)
	at org.jetbrains.kotlin.config.phaser.SimpleNamedCompilerPhase.phaseBody(CompilerPhase.kt:215)
	at org.jetbrains.kotlin.config.phaser.NamedCompilerPhase.invoke(CompilerPhase.kt:111)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invokeSequential(performByIrFile.kt:51)
	... 45 more
Caused by: java.lang.RuntimeException: Exception while generating code for:
FUN name:invokeSuspend visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>, $result:kotlin.Result<kotlin.Any?>) returnType:kotlin.Any?
  overridden:
    protected abstract fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in kotlin.coroutines.jvm.internal.SuspendLambda
  $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>
  VALUE_PARAMETER name:$result index:0 type:kotlin.Result<kotlin.Any?>
  BLOCK_BODY
    VAR SUSPEND_LAMBDA_PARAMETER name:$this$launch type:kotlinx.coroutines.CoroutineScope [val]
      BLOCK type=kotlinx.coroutines.CoroutineScope origin=null
        GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:private declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlinx.coroutines.CoroutineScope origin=null
          receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
    VAR name:baseConfigObj type:com.asmtunis.procaisseinventory.core.model.GenericObject [val]
      CONSTRUCTOR_CALL 'public constructor <init> (connexion: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig, data: kotlinx.serialization.json.JsonElement?) [primary] declared in com.asmtunis.procaisseinventory.core.model.GenericObject' type=com.asmtunis.procaisseinventory.core.model.GenericObject origin=null
        connexion: BLOCK type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig origin=FOLDED_ELVIS
          WHEN type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig origin=FOLDED_ELVIS
            BRANCH
              if: CALL 'public final fun ANDAND (arg0: kotlin.Boolean, arg1: kotlin.Boolean): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                arg0: COMPOSITE type=kotlin.Boolean origin=null
                  VAR IR_TEMPORARY_VARIABLE name:tmp0_safe_receiver type:kotlin.String? [val]
                    CALL 'public final fun first <T> ($completion: kotlin.coroutines.Continuation<T of kotlinx.coroutines.flow.FlowKt.first>): kotlin.Any? [suspend] declared in kotlinx.coroutines.flow.FlowKt' type=kotlin.String? origin=null
                      <T>: kotlin.String?
                      $receiver: CALL 'public open fun getString$default ($this: com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository, key: kotlin.String, default: kotlin.String?, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlinx.coroutines.flow.Flow<kotlin.String?> declared in com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository.DefaultImpls' type=kotlinx.coroutines.flow.Flow<kotlin.String?> origin=DEFAULT_DISPATCH_CALL
                        $this: CALL 'public final fun <get-dataStore> (): com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository declared in com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb' type=com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository origin=GET_PROPERTY
                          $this: CALL 'public final fun access$getProCaisseLocalDb$p ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel): com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb origin=null
                            $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                              receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                        key: CONST String type=kotlin.String value="Seleceted_BASE_CONFIG"
                        default: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                          CONST Null type=kotlin.String? value=null
                        $mask0: CONST Int type=kotlin.Int value=2
                        $handler: CONST Null type=kotlin.Any? value=null
                      $completion: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                  CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                    $this: CALL 'public final fun EQEQ (arg0: kotlin.Any?, arg1: kotlin.Any?): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                      arg0: GET_VAR 'val tmp0_safe_receiver: kotlin.String? [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=kotlin.String? origin=null
                      arg1: CONST Null type=kotlin.Nothing? value=null
                arg1: COMPOSITE type=kotlin.Boolean origin=null
                  VAR IR_TEMPORARY_VARIABLE name:tmp1_elvis_lhs type:com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? [val]
                    CALL 'public final fun let <T, R> (block: kotlin.Function1<T of kotlin.StandardKt.let, R of kotlin.StandardKt.let>): R of kotlin.StandardKt.let [inline] declared in kotlin.StandardKt' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? origin=null
                      <T>: kotlin.String
                      <R>: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig?
                      $receiver: GET_VAR 'val tmp0_safe_receiver: kotlin.String? [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=kotlin.String? origin=null
                      block: BLOCK type=kotlin.Function1<kotlin.String, com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig?> origin=LAMBDA
                        COMPOSITE type=kotlin.Unit origin=null
                        FUNCTION_REFERENCE 'private final fun invokeSuspend$lambda$0 (it: kotlin.String): com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=kotlin.Function1<kotlin.String, com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig?> origin=INLINE_LAMBDA reflectionTarget=null
                  CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                    $this: CALL 'public final fun EQEQ (arg0: kotlin.Any?, arg1: kotlin.Any?): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                      arg0: GET_VAR 'val tmp1_elvis_lhs: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? origin=null
                      arg1: CONST Null type=kotlin.Nothing? value=null
              then: GET_VAR 'val tmp1_elvis_lhs: com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig? origin=null
            BRANCH
              if: CONST Boolean type=kotlin.Boolean value=true
              then: CONSTRUCTOR_CALL 'public constructor <init> (id_base_config: kotlin.String?, key_base: kotlin.String?, dbName: kotlin.String?, dbIpAddress: kotlin.String?, adresse_ip: kotlin.String?, port: kotlin.String?, username: kotlin.String?, password: kotlin.String?, designation_base: kotlin.String?, produit: kotlin.String?, id_entreprise: kotlin.String?, date_creation: kotlin.String?, licences: kotlin.collections.List<com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence>?, $mask0: kotlin.Int, $marker: kotlin.jvm.internal.DefaultConstructorMarker?) declared in com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig' type=com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig origin=DEFAULT_DISPATCH_CALL
                id_base_config: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                key_base: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                dbName: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                dbIpAddress: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                adresse_ip: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                port: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                username: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                password: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                designation_base: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                produit: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                id_entreprise: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                date_creation: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.String? value=null
                licences: COMPOSITE type=kotlin.collections.List<com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence>? origin=DEFAULT_VALUE
                  CONST Null type=kotlin.collections.List<com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence>? value=null
                $mask0: CONST Int type=kotlin.Int value=8191
                $marker: CONST Null type=kotlin.jvm.internal.DefaultConstructorMarker? value=null
        data: CALL 'public final fun encodeToJsonElement <T> (value: T of kotlinx.serialization.json.JsonKt.encodeToJsonElement): kotlinx.serialization.json.JsonElement [inline] declared in kotlinx.serialization.json.JsonKt' type=kotlinx.serialization.json.JsonElement origin=null
          <T>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments>
          $receiver: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Default type:kotlinx.serialization.json.Json.Default visibility:public [final,static] declared in kotlinx.serialization.json.Json' type=kotlinx.serialization.json.Json.Default origin=null
          value: CALL 'public final fun <get-ticketsWithLinesAndPaymentsNotSync> (): kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> origin=GET_PROPERTY
            $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
              receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
    COMPOSITE type=kotlin.Unit origin=null
      CALL 'public final fun launchIn <T> (scope: kotlinx.coroutines.CoroutineScope): kotlinx.coroutines.Job declared in kotlinx.coroutines.flow.FlowKt' type=kotlinx.coroutines.Job origin=null
        <T>: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
        $receiver: CALL 'public final fun flowOn <T> (context: kotlin.coroutines.CoroutineContext): kotlinx.coroutines.flow.Flow<T of kotlinx.coroutines.flow.FlowKt.flowOn> declared in kotlinx.coroutines.flow.FlowKt' type=kotlinx.coroutines.flow.Flow<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>> origin=null
          <T>: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
          $receiver: CALL 'public final fun onEach <T> (action: kotlin.coroutines.SuspendFunction1<T of kotlinx.coroutines.flow.FlowKt.onEach, kotlin.Unit>): kotlinx.coroutines.flow.Flow<T of kotlinx.coroutines.flow.FlowKt.onEach> declared in kotlinx.coroutines.flow.FlowKt' type=kotlinx.coroutines.flow.Flow<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>> origin=null
            <T>: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
            $receiver: CALL 'public abstract fun addBatchFactureWithLines (baseConfig: kotlin.String, $completion: kotlin.coroutines.Continuation<kotlinx.coroutines.flow.Flow<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>>>): kotlin.Any? [suspend] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApi' type=kotlinx.coroutines.flow.Flow<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>> origin=null
              $this: CALL 'public final fun <get-ticket> (): com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApi declared in com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApi origin=GET_PROPERTY
                $this: CALL 'public final fun access$getProCaisseRemote$p ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel): com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote origin=null
                  $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                    receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
              baseConfig: CALL 'public final fun encodeToString <T> (value: T of kotlinx.serialization.json.Json.encodeToString): kotlin.String [inline] declared in kotlinx.serialization.json.Json' type=kotlin.String origin=null
                <T>: com.asmtunis.procaisseinventory.core.model.GenericObject
                $this: TYPE_OP type=kotlinx.serialization.json.Json origin=IMPLICIT_CAST typeOperand=kotlinx.serialization.json.Json
                  GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:Default type:kotlinx.serialization.json.Json.Default visibility:public [final,static] declared in kotlinx.serialization.json.Json' type=kotlinx.serialization.json.Json.Default origin=null
                value: GET_VAR 'val baseConfigObj: com.asmtunis.procaisseinventory.core.model.GenericObject [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.GenericObject origin=null
              $completion: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
            action: BLOCK type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
              CLASS SUSPEND_LAMBDA CLASS name:<no name provided> modality:FINAL visibility:public/*package*/ superTypes:[kotlin.coroutines.jvm.internal.SuspendLambda; kotlin.jvm.functions.Function2<com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>, kotlin.coroutines.Continuation<kotlin.Unit>?, kotlin.Any?>]
                $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                FIELD name:label type:kotlin.Int visibility:public/*package*/
                FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:public/*package*/
                CONSTRUCTOR SUSPEND_LAMBDA visibility:public/*package*/ <> (this$0:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion:kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>?) returnType:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> [primary]
                  VALUE_PARAMETER BOUND_RECEIVER_PARAMETER name:this$0 index:0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel
                  VALUE_PARAMETER name:$completion index:1 type:kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>?
                  BLOCK_BODY
                    SET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.Unit origin=INITIALIZER_OF_FIELD_FOR_CAPTURED_VALUE
                      receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                      value: GET_VAR 'this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.<init>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                    DELEGATING_CONSTRUCTOR_CALL 'public constructor <init> (arity: kotlin.Int, $completion: kotlin.coroutines.Continuation<kotlin.Any?>?) declared in kotlin.coroutines.jvm.internal.SuspendLambda'
                      arity: CONST Int type=kotlin.Int value=2
                      $completion: GET_VAR '$completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.<init>' type=kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>? origin=null
                    BLOCK type=kotlin.Unit origin=null
                FUN name:invokeSuspend visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>, $result:kotlin.Result<kotlin.Any?>) returnType:kotlin.Any?
                  overridden:
                    protected abstract fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in kotlin.coroutines.jvm.internal.SuspendLambda
                  $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                  VALUE_PARAMETER name:$result index:0 type:kotlin.Result<kotlin.Any?>
                  BLOCK_BODY
                    VAR SUSPEND_LAMBDA_PARAMETER name:result type:com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val]
                      BLOCK type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                        GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:public/*package*/ declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                          receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                    BLOCK type=kotlin.Unit origin=WHEN
                      VAR IR_TEMPORARY_VARIABLE name:tmp0_subject type:com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val]
                        GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                      WHEN type=kotlin.Unit origin=WHEN
                        BRANCH
                          if: TYPE_OP type=kotlin.Boolean origin=INSTANCEOF typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                            GET_VAR 'val tmp0_subject: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                          then: BLOCK type=kotlin.Unit origin=null
                            CALL 'public final fun access$setResponseFactureTicket ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                              $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                              <set-?>: CONSTRUCTOR_CALL 'public constructor <init> (data: T of com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState?, loading: kotlin.Boolean, error: kotlin.String?, message: kotlin.String?, $mask0: kotlin.Int, $marker: kotlin.jvm.internal.DefaultConstructorMarker?) declared in com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState' type=com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=DEFAULT_DISPATCH_CALL
                                <class: T>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>
                                data: CALL 'public final fun <get-data> (): T of com.asmtunis.procaisseinventory.core.model.DataResult.Success? [fake_override] declared in com.asmtunis.procaisseinventory.core.model.DataResult.Success' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>? origin=GET_PROPERTY
                                  $this: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                    GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                loading: CONST Boolean type=kotlin.Boolean value=false
                                error: CONST Null type=kotlin.Nothing? value=null
                                message: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                                  CONST Null type=kotlin.String? value=null
                                $mask0: CONST Int type=kotlin.Int value=8
                                $marker: CONST Null type=kotlin.jvm.internal.DefaultConstructorMarker? value=null
                            CALL 'public final fun access$setTicketsWithLinesAndPaymentsNotSync ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                              $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                              <set-?>: CALL 'public final fun emptyList <T> (): kotlin.collections.List<T of kotlin.collections.CollectionsKt.emptyList> declared in kotlin.collections.CollectionsKt' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> origin=null
                                <T>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
                            BLOCK type=kotlin.Unit origin=FOR_LOOP
                              COMPOSITE type=kotlin.Unit origin=null
                                VAR FOR_LOOP_VARIABLE name:i type:kotlin.Int [var]
                                  CONST Int type=kotlin.Int value=0
                                VAR IR_TEMPORARY_VARIABLE name:last type:kotlin.Int [val]
                                  CALL 'public abstract fun <get-size> (): kotlin.Int declared in kotlin.collections.Collection' type=kotlin.Int origin=null
                                    $this: CALL 'public final fun CHECK_NOT_NULL <T0> (arg0: T0 of kotlin.internal.ir.CHECK_NOT_NULL?): {T0 of kotlin.internal.ir.CHECK_NOT_NULL & Any} declared in kotlin.internal.ir' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate> origin=EXCLEXCL
                                      <T0>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>
                                      arg0: CALL 'public final fun <get-data> (): T of com.asmtunis.procaisseinventory.core.model.DataResult.Success? [fake_override] declared in com.asmtunis.procaisseinventory.core.model.DataResult.Success' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>? origin=GET_PROPERTY
                                        $this: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                          GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                              DO_WHILE label=SyncBonLivraisonViewModel$facturerBL$<no name provided>$invokeSuspend$<no name provided>$invokeSuspend2 origin=DO_WHILE_COUNTER_LOOP
                                body: COMPOSITE type=kotlin.Unit origin=FOR_LOOP_INNER_WHILE
                                  COMPOSITE type=kotlin.Unit origin=FOR_LOOP_NEXT
                                    WHEN type=kotlin.Unit origin=null
                                      BRANCH
                                        if: CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                          $this: CALL 'public final fun less (arg0: kotlin.Int, arg1: kotlin.Int): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                                            arg0: GET_VAR 'var i: kotlin.Int [var] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=kotlin.Int origin=null
                                            arg1: GET_VAR 'val last: kotlin.Int [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=kotlin.Int origin=null
                                        then: BREAK label=null loop.label=SyncBonLivraisonViewModel$facturerBL$<no name provided>$invokeSuspend$<no name provided>$invokeSuspend2
                                  BLOCK type=kotlin.Unit origin=null
                                    VAR name:ticketUpdate type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val]
                                      CALL 'public abstract fun get (index: kotlin.Int): E of kotlin.collections.List [operator] declared in kotlin.collections.List' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=GET_ARRAY_ELEMENT
                                        $this: CALL 'public final fun <get-data> (): T of com.asmtunis.procaisseinventory.core.model.DataResult.Success? [fake_override] declared in com.asmtunis.procaisseinventory.core.model.DataResult.Success' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>? origin=GET_PROPERTY
                                          $this: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Success<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                            GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                        index: GET_VAR 'var i: kotlin.Int [var] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=kotlin.Int origin=null
                                    WHEN type=kotlin.Unit origin=IF
                                      BRANCH
                                        if: CALL 'public final fun ANDAND (arg0: kotlin.Boolean, arg1: kotlin.Boolean): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                                          arg0: CALL 'public final fun ANDAND (arg0: kotlin.Boolean, arg1: kotlin.Boolean): kotlin.Boolean declared in kotlin.internal.ir' type=kotlin.Boolean origin=null
                                            arg0: CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                              $this: CALL 'public final fun equals$default (other: kotlin.String?, ignoreCase: kotlin.Boolean, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Boolean declared in kotlin.text.StringsKt' type=kotlin.Boolean origin=DEFAULT_DISPATCH_CALL
                                                $receiver: CALL 'public final fun <get-code> (): kotlin.String? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate' type=kotlin.String? origin=GET_PROPERTY
                                                  $this: GET_VAR 'val ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=null
                                                other: CONST String type=kotlin.String value="10200"
                                                ignoreCase: COMPOSITE type=kotlin.Boolean origin=DEFAULT_VALUE
                                                  CONST Boolean type=kotlin.Boolean value=false
                                                $mask0: CONST Int type=kotlin.Int value=2
                                                $handler: CONST Null type=kotlin.Any? value=null
                                            arg1: CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                              $this: CALL 'public final fun equals$default (other: kotlin.String?, ignoreCase: kotlin.Boolean, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Boolean declared in kotlin.text.StringsKt' type=kotlin.Boolean origin=DEFAULT_DISPATCH_CALL
                                                $receiver: CALL 'public final fun <get-code> (): kotlin.String? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate' type=kotlin.String? origin=GET_PROPERTY
                                                  $this: GET_VAR 'val ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=null
                                                other: CONST String type=kotlin.String value="10201"
                                                ignoreCase: COMPOSITE type=kotlin.Boolean origin=DEFAULT_VALUE
                                                  CONST Boolean type=kotlin.Boolean value=false
                                                $mask0: CONST Int type=kotlin.Int value=2
                                                $handler: CONST Null type=kotlin.Any? value=null
                                          arg1: CALL 'public final fun not (): kotlin.Boolean [operator] declared in kotlin.Boolean' type=kotlin.Boolean origin=null
                                            $this: CALL 'public final fun equals$default (other: kotlin.String?, ignoreCase: kotlin.Boolean, $mask0: kotlin.Int, $handler: kotlin.Any?): kotlin.Boolean declared in kotlin.text.StringsKt' type=kotlin.Boolean origin=DEFAULT_DISPATCH_CALL
                                              $receiver: CALL 'public final fun <get-code> (): kotlin.String? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate' type=kotlin.String? origin=GET_PROPERTY
                                                $this: GET_VAR 'val ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=null
                                              other: CONST String type=kotlin.String value="10304"
                                              ignoreCase: COMPOSITE type=kotlin.Boolean origin=DEFAULT_VALUE
                                                CONST Boolean type=kotlin.Boolean value=false
                                              $mask0: CONST Int type=kotlin.Int value=2
                                              $handler: CONST Null type=kotlin.Any? value=null
                                        then: BLOCK type=kotlin.Unit origin=null
                                          RETURN type=kotlin.Nothing from='public final fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>'
                                            GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:kotlin.Unit visibility:public [final,static] declared in kotlin.Unit' type=kotlin.Unit origin=null
                                    CALL 'public final fun access$updateLocalData ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate, insertFacture: kotlin.Boolean): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=null
                                      $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                        receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                                      ticketUpdate: GET_VAR 'val ticketUpdate: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate origin=null
                                      insertFacture: CONST Boolean type=kotlin.Boolean value=true
                                condition: COMPOSITE type=kotlin.Boolean origin=null
                                  CALL 'public final fun <int-prefix-incr-decr> (value: kotlin.Int, delta: kotlin.Int): kotlin.Int declared in kotlin.jvm.internal' type=kotlin.Int origin=null
                                    value: GET_VAR 'var i: kotlin.Int [var] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=kotlin.Int origin=null
                                    delta: CONST Int type=kotlin.Int value=1
                                  CONST Boolean type=kotlin.Boolean value=true
                        BRANCH
                          if: TYPE_OP type=kotlin.Boolean origin=INSTANCEOF typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Loading<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                            GET_VAR 'val tmp0_subject: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                          then: BLOCK type=kotlin.Unit origin=null
                            CALL 'public final fun access$setResponseFactureTicket ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                              $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                              <set-?>: CONSTRUCTOR_CALL 'public constructor <init> (data: T of com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState?, loading: kotlin.Boolean, error: kotlin.String?, message: kotlin.String?, $mask0: kotlin.Int, $marker: kotlin.jvm.internal.DefaultConstructorMarker?) declared in com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState' type=com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=DEFAULT_DISPATCH_CALL
                                <class: T>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>
                                data: CONST Null type=kotlin.Nothing? value=null
                                loading: CONST Boolean type=kotlin.Boolean value=true
                                error: CONST Null type=kotlin.Nothing? value=null
                                message: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                                  CONST Null type=kotlin.String? value=null
                                $mask0: CONST Int type=kotlin.Int value=8
                                $marker: CONST Null type=kotlin.jvm.internal.DefaultConstructorMarker? value=null
                        BRANCH
                          if: TYPE_OP type=kotlin.Boolean origin=INSTANCEOF typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Error<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                            GET_VAR 'val tmp0_subject: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                          then: BLOCK type=kotlin.Unit origin=null
                            CALL 'public final fun access$setTicketsWithLinesAndPaymentsNotSync ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                              $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                              <set-?>: CALL 'public final fun emptyList <T> (): kotlin.collections.List<T of kotlin.collections.CollectionsKt.emptyList> declared in kotlin.collections.CollectionsKt' type=kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments> origin=null
                                <T>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
                            CALL 'public final fun access$setResponseFactureTicket ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, <set-?>: com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>): kotlin.Unit declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlin.Unit origin=EQ
                              $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                                receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                              <set-?>: CONSTRUCTOR_CALL 'public constructor <init> (data: T of com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState?, loading: kotlin.Boolean, error: kotlin.String?, message: kotlin.String?, $mask0: kotlin.Int, $marker: kotlin.jvm.internal.DefaultConstructorMarker?) declared in com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState' type=com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=DEFAULT_DISPATCH_CALL
                                <class: T>: kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>
                                data: CONST Null type=kotlin.Nothing? value=null
                                loading: CONST Boolean type=kotlin.Boolean value=false
                                error: CALL 'public final fun <get-message> (): kotlin.String? [fake_override] declared in com.asmtunis.procaisseinventory.core.model.DataResult.Error' type=kotlin.String? origin=GET_PROPERTY
                                  $this: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult.Error<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult.Error<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                                    GET_VAR 'val result: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                                message: COMPOSITE type=kotlin.String? origin=DEFAULT_VALUE
                                  CONST Null type=kotlin.String? value=null
                                $mask0: CONST Int type=kotlin.Int value=8
                                $marker: CONST Null type=kotlin.jvm.internal.DefaultConstructorMarker? value=null
                        BRANCH
                          if: CONST Boolean type=kotlin.Boolean value=true
                          then: BLOCK type=kotlin.Nothing origin=null
                            CALL 'public final fun noWhenBranchMatchedException (): kotlin.Nothing declared in kotlin.internal.ir' type=kotlin.Nothing origin=null
                            CALL 'public final fun throwKotlinNothingValueException (): kotlin.Nothing declared in kotlin.jvm.internal.Intrinsics' type=kotlin.Nothing origin=null
                FUN name:create visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>, value:kotlin.Any?, $completion:kotlin.coroutines.Continuation<kotlin.Nothing>) returnType:kotlin.coroutines.Continuation<kotlin.Unit>
                  overridden:
                    public open fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in kotlin.coroutines.jvm.internal.SuspendLambda
                  $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                  VALUE_PARAMETER name:value index:0 type:kotlin.Any?
                  VALUE_PARAMETER name:$completion index:1 type:kotlin.coroutines.Continuation<kotlin.Nothing>
                  BLOCK_BODY
                    VAR IR_TEMPORARY_VARIABLE name:tmp0_result type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> [val]
                      CONSTRUCTOR_CALL 'public/*package*/ constructor <init> (this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>?) [primary] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                        this$0: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                          receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                        $completion: GET_VAR '$completion: kotlin.coroutines.Continuation<kotlin.Nothing> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=kotlin.coroutines.Continuation<kotlin.Nothing> origin=null
                    SET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:L$0 type:kotlin.Any? visibility:public/*package*/ declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.Unit origin=null
                      receiver: GET_VAR 'val tmp0_result: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                      value: GET_VAR 'value: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=kotlin.Any? origin=null
                    RETURN type=kotlin.Nothing from='public final fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>'
                      GET_VAR 'val tmp0_result: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.create' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                FUN name:invoke visibility:public modality:FINAL <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>, p1:com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>, p2:kotlin.coroutines.Continuation<kotlin.Unit>?) returnType:kotlin.Any?
                  overridden:
                    public abstract fun invoke (p1: P1 of kotlin.jvm.functions.Function2, p2: P2 of kotlin.jvm.functions.Function2): R of kotlin.jvm.functions.Function2 declared in kotlin.jvm.functions.Function2
                  $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                  VALUE_PARAMETER name:p1 index:0 type:com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>
                  VALUE_PARAMETER name:p2 index:1 type:kotlin.coroutines.Continuation<kotlin.Unit>?
                  BLOCK_BODY
                    RETURN type=kotlin.Nothing from='public final fun invoke (p1: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>, p2: kotlin.coroutines.Continuation<kotlin.Unit>?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>'
                      CALL 'public final fun invokeSuspend ($result: kotlin.Result<kotlin.Any?>): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.Any? origin=null
                        $this: TYPE_OP type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                          CALL 'public final fun create (value: kotlin.Any?, $completion: kotlin.coroutines.Continuation<kotlin.Nothing>): kotlin.coroutines.Continuation<kotlin.Unit> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.coroutines.Continuation<kotlin.Unit> origin=null
                            $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                            value: GET_VAR 'p1: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>> origin=null
                            $completion: GET_VAR 'p2: kotlin.coroutines.Continuation<kotlin.Unit>? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=kotlin.coroutines.Continuation<kotlin.Unit>? origin=null
                        $result: CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=kotlin.Result<kotlin.Any?> origin=null
                          <T>: kotlin.Any?
                          <R>: kotlin.Result<kotlin.Any?>
                          v: GET_FIELD 'FIELD FIELD_FOR_OBJECT_INSTANCE name:INSTANCE type:kotlin.Unit visibility:public [final,static] declared in kotlin.Unit' type=kotlin.Unit origin=null
                FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final]
                FUN BRIDGE name:invoke visibility:public modality:OPEN <> ($this:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>, p1:kotlin.Any?, p2:kotlin.Any?) returnType:kotlin.Any?
                  $this: VALUE_PARAMETER INSTANCE_RECEIVER name:<this> type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>
                  VALUE_PARAMETER BRIDGE name:p1 index:0 type:kotlin.Any?
                  VALUE_PARAMETER BRIDGE name:p2 index:1 type:kotlin.Any?
                  EXPRESSION_BODY
                    RETURN type=kotlin.Nothing from='public open fun invoke (p1: kotlin.Any?, p2: kotlin.Any?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>'
                      CALL 'public final fun invoke (p1: com.asmtunis.procaisseinventory.core.model.DataResult<kotlin.collections.List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate>>, p2: kotlin.coroutines.Continuation<kotlin.Unit>?): kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=kotlin.Any? origin=BRIDGE_DELEGATION
                        $this: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                        p1: TYPE_OP type=com.asmtunis.procaisseinventory.core.model.DataResult<*> origin=IMPLICIT_CAST typeOperand=com.asmtunis.procaisseinventory.core.model.DataResult<*>
                          GET_VAR 'p1: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=kotlin.Any? origin=null
                        p2: TYPE_OP type=kotlin.coroutines.Continuation<*> origin=IMPLICIT_CAST typeOperand=kotlin.coroutines.Continuation<*>
                          GET_VAR 'p2: kotlin.Any? declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>.invoke' type=kotlin.Any? origin=null
              CONSTRUCTOR_CALL 'public/*package*/ constructor <init> (this$0: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel, $completion: kotlin.coroutines.Continuation<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>>?) [primary] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend.<no name provided> origin=null
                this$0: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
                  receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
                $completion: CONST Null type=kotlin.Nothing? value=null
          context: CALL 'public final fun access$getDispatcherIO$p ($this: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel): kotlinx.coroutines.CoroutineDispatcher declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel' type=kotlinx.coroutines.CoroutineDispatcher origin=null
            $this: GET_FIELD 'FIELD FIELD_FOR_CAPTURED_VALUE name:this$0 type:com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel visibility:public/*package*/ [final] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel origin=null
              receiver: GET_VAR '<this>: com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided> origin=null
        scope: GET_VAR 'val $this$launch: kotlinx.coroutines.CoroutineScope [val] declared in com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel.facturerBL.<no name provided>.invokeSuspend' type=kotlinx.coroutines.CoroutineScope origin=null
      COMPOSITE type=kotlin.Unit origin=null

	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:48)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate$default(FunctionCodegen.kt:41)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethodNode(ClassCodegen.kt:405)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethod(ClassCodegen.kt:422)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generate(ClassCodegen.kt:167)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitClass(ExpressionCodegen.kt:955)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitClass(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.declarations.IrClass.accept(IrClass.kt:72)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:520)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:534)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitor$DefaultImpls.visitBlock(IrElementVisitor.kt:136)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlock(ExpressionCodegen.kt:406)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlock(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrBlock.accept(IrBlock.kt:18)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.gen(ExpressionCodegen.kt:214)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrCallGenerator.genValueAndPut(IrCallGenerator.kt:48)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall$handleParameter(ExpressionCodegen.kt:557)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:585)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:520)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:534)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitor$DefaultImpls.visitComposite(IrElementVisitor.kt:139)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitComposite(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitComposite(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrComposite.accept(IrComposite.kt:18)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:520)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:525)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrBlockBody.accept(IrBlockBody.kt:20)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.generate(ExpressionCodegen.kt:237)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.doGenerate(FunctionCodegen.kt:124)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:45)
	... 54 more
Caused by: org.jetbrains.kotlin.codegen.CompilationException: Back-end (JVM) Internal error: Couldn't transform method node:
invokeSuspend (Ljava/lang/Object;)Ljava/lang/Object;:
    // annotable parameter count: 1 (visible)
    // annotable parameter count: 1 (invisible)
   L0
    ALOAD 0
    GETFIELD com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel$facturerBL$1$1.L$0 : Ljava/lang/Object;
    CHECKCAST com/asmtunis/procaisseinventory/core/model/DataResult
    ASTORE 2
    BIPUSH 10
    INVOKESTATIC kotlin/jvm/internal/InlineMarker.mark (I)V
   L1
   L2
    LINENUMBER 224 L2
    ALOAD 2
    ASTORE 3
   L3
    NOP
   L4
    LINENUMBER 225 L4
    ALOAD 3
    INSTANCEOF com/asmtunis/procaisseinventory/core/model/DataResult$Success
    IFEQ L5
   L6
    LINENUMBER 226 L6
    ALOAD 0
    GETFIELD com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel$facturerBL$1$1.this$0 : Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;
    NEW com/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState
    DUP
    ALOAD 2
    CHECKCAST com/asmtunis/procaisseinventory/core/model/DataResult$Success
    INVOKEVIRTUAL com/asmtunis/procaisseinventory/core/model/DataResult$Success.getData ()Ljava/lang/Object;
    NOP
    ICONST_0
    ACONST_NULL
    ACONST_NULL
    BIPUSH 8
    ACONST_NULL
    INVOKESPECIAL com/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState.<init> (Ljava/lang/Object;ZLjava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    INVOKESTATIC com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel.access$setResponseFactureTicket (Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;Lcom/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState;)V
   L7
    LINENUMBER 227 L7
    ALOAD 0
    GETFIELD com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel$facturerBL$1$1.this$0 : Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;
    INVOKESTATIC kotlin/collections/CollectionsKt.emptyList ()Ljava/util/List;
    INVOKESTATIC com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel.access$setTicketsWithLinesAndPaymentsNotSync (Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;Ljava/util/List;)V
   L8
    LINENUMBER 228 L8
    ICONST_0
    ISTORE 4
   L9
    ALOAD 2
    CHECKCAST com/asmtunis/procaisseinventory/core/model/DataResult$Success
    INVOKEVIRTUAL com/asmtunis/procaisseinventory/core/model/DataResult$Success.getData ()Ljava/lang/Object;
    DUP
    INVOKESTATIC kotlin/jvm/internal/Intrinsics.checkNotNull (Ljava/lang/Object;)V
    CHECKCAST java/util/Collection
    INVOKEINTERFACE java/util/Collection.size ()I (itf)
    ISTORE 5
   L10
   L11
    ILOAD 4
    ILOAD 5
    IF_ICMPLT L12
    NOP
    GOTO L13
   L14
   L12
   L15
   L16
    LINENUMBER 229 L16
    ALOAD 2
    CHECKCAST com/asmtunis/procaisseinventory/core/model/DataResult$Success
    INVOKEVIRTUAL com/asmtunis/procaisseinventory/core/model/DataResult$Success.getData ()Ljava/lang/Object;
    CHECKCAST java/util/List
    ILOAD 4
    INVOKEINTERFACE java/util/List.get (I)Ljava/lang/Object; (itf)
    CHECKCAST com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/data/domaine/TicketUpdate
    ASTORE 6
   L17
   L18
    LINENUMBER 230 L18
    ALOAD 6
    INVOKEVIRTUAL com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/data/domaine/TicketUpdate.getCode ()Ljava/lang/String;
    LDC "10200"
    NOP
    ICONST_0
    ICONST_2
    ACONST_NULL
    INVOKESTATIC kotlin/text/StringsKt.equals$default (Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z
    IFNE L19
    ALOAD 6
    INVOKEVIRTUAL com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/data/domaine/TicketUpdate.getCode ()Ljava/lang/String;
    LDC "10201"
    NOP
    ICONST_0
    ICONST_2
    ACONST_NULL
    INVOKESTATIC kotlin/text/StringsKt.equals$default (Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z
    IFNE L19
    ALOAD 6
    INVOKEVIRTUAL com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/data/domaine/TicketUpdate.getCode ()Ljava/lang/String;
    LDC "10304"
    NOP
    ICONST_0
    ICONST_2
    ACONST_NULL
    INVOKESTATIC kotlin/text/StringsKt.equals$default (Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z
    IFNE L19
   L20
    LINENUMBER 232 L20
    GETSTATIC kotlin/Unit.INSTANCE : Lkotlin/Unit;
    ARETURN
   L21
   L19
   L22
   L23
    LINENUMBER 235 L23
    ALOAD 0
    GETFIELD com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel$facturerBL$1$1.this$0 : Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;
    ALOAD 6
    NOP
    ICONST_1
    INVOKESTATIC com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel.access$updateLocalData (Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/data/domaine/TicketUpdate;Z)V
   L24
   L25
   L26
    LINENUMBER 228 L26
    IINC 4 1
    ILOAD 4
    POP
    NOP
    GOTO L11
   L13
   L27
    GOTO L28
   L5
   L29
    LINENUMBER 239 L29
    ALOAD 3
    INSTANCEOF com/asmtunis/procaisseinventory/core/model/DataResult$Loading
    IFEQ L30
   L31
    LINENUMBER 240 L31
    ALOAD 0
    GETFIELD com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel$facturerBL$1$1.this$0 : Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;
    NEW com/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState
    DUP
    ACONST_NULL
    NOP
    ICONST_1
    ACONST_NULL
    ACONST_NULL
    BIPUSH 8
    ACONST_NULL
    INVOKESPECIAL com/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState.<init> (Ljava/lang/Object;ZLjava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    INVOKESTATIC com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel.access$setResponseFactureTicket (Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;Lcom/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState;)V
    GOTO L28
   L30
   L32
    LINENUMBER 243 L32
    ALOAD 3
    INSTANCEOF com/asmtunis/procaisseinventory/core/model/DataResult$Error
    IFEQ L33
   L34
    LINENUMBER 244 L34
    ALOAD 0
    GETFIELD com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel$facturerBL$1$1.this$0 : Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;
    INVOKESTATIC kotlin/collections/CollectionsKt.emptyList ()Ljava/util/List;
    INVOKESTATIC com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel.access$setTicketsWithLinesAndPaymentsNotSync (Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;Ljava/util/List;)V
   L35
    LINENUMBER 245 L35
    ALOAD 0
    GETFIELD com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel$facturerBL$1$1.this$0 : Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;
    NEW com/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState
    DUP
    ACONST_NULL
    NOP
    ICONST_0
    ALOAD 2
    CHECKCAST com/asmtunis/procaisseinventory/core/model/DataResult$Error
    INVOKEVIRTUAL com/asmtunis/procaisseinventory/core/model/DataResult$Error.getMessage ()Ljava/lang/String;
    ACONST_NULL
    BIPUSH 8
    ACONST_NULL
    INVOKESPECIAL com/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState.<init> (Ljava/lang/Object;ZLjava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    INVOKESTATIC com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel.access$setResponseFactureTicket (Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel;Lcom/asmtunis/procaisseinventory/core/ktor/domaine/RemoteResponseState;)V
    GOTO L28
   L33
   L36
    LINENUMBER 224 L36
    NEW kotlin/NoWhenBranchMatchedException
    DUP
    INVOKESPECIAL kotlin/NoWhenBranchMatchedException.<init> ()V
    ATHROW
   L37
   L28
   L38
   L39
    LINENUMBER 248 L39
    NOP
    GETSTATIC kotlin/Unit.INSTANCE : Lkotlin/Unit;
    ARETURN
   L40
    LOCALVARIABLE ticketUpdate Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/data/domaine/TicketUpdate; L17 L24 6
    LOCALVARIABLE i I L9 L27 4
    LOCALVARIABLE result Lcom/asmtunis/procaisseinventory/core/model/DataResult; L1 L40 2
    LOCALVARIABLE this Lcom/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel$facturerBL$1$1; L0 L40 0
    LOCALVARIABLE $result Ljava/lang/Object; L0 L40 1
    MAXSTACK = 9
    MAXLOCALS = 7

File is unknown
The root cause java.lang.OutOfMemoryError was thrown at: java.base/java.util.ArrayList.<init>(ArrayList.java:156)
	at org.jetbrains.kotlin.codegen.TransformationMethodVisitor.visitEnd(TransformationMethodVisitor.kt:89)
	at org.jetbrains.org.objectweb.asm.tree.MethodNode.accept(MethodNode.java:770)
	at org.jetbrains.kotlin.backend.jvm.codegen.CoroutineCodegenKt.acceptWithStateMachine(CoroutineCodegen.kt:73)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethod(ClassCodegen.kt:439)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generate(ClassCodegen.kt:167)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitClass(ExpressionCodegen.kt:955)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitClass(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.declarations.IrClass.accept(IrClass.kt:72)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:520)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:534)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitor$DefaultImpls.visitBlock(IrElementVisitor.kt:136)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlock(ExpressionCodegen.kt:406)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlock(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrBlock.accept(IrBlock.kt:18)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.gen(ExpressionCodegen.kt:214)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrCallGenerator.genValueAndPut(IrCallGenerator.kt:48)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall$handleParameter(ExpressionCodegen.kt:557)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:585)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.gen(ExpressionCodegen.kt:214)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrCallGenerator.genValueAndPut(IrCallGenerator.kt:48)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall$handleParameter(ExpressionCodegen.kt:557)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:580)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.gen(ExpressionCodegen.kt:214)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrCallGenerator.genValueAndPut(IrCallGenerator.kt:48)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall$handleParameter(ExpressionCodegen.kt:557)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:580)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:520)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:534)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitor$DefaultImpls.visitComposite(IrElementVisitor.kt:139)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitComposite(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitComposite(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrComposite.accept(IrComposite.kt:18)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:520)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:525)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrBlockBody.accept(IrBlockBody.kt:20)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.generate(ExpressionCodegen.kt:237)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.doGenerate(FunctionCodegen.kt:124)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:45)
	... 88 more
Caused by: java.lang.OutOfMemoryError: GC overhead limit exceeded
	at java.base/java.util.ArrayList.<init>(ArrayList.java:156)
	at org.jetbrains.kotlin.codegen.optimization.common.ControlFlowGraph.<init>(ControlFlowGraph.kt:13)
	at org.jetbrains.kotlin.codegen.optimization.common.ControlFlowGraph.<init>(ControlFlowGraph.kt)
	at org.jetbrains.kotlin.codegen.optimization.common.ControlFlowGraph$Builder.build(ControlFlowGraph.kt:39)
	at org.jetbrains.kotlin.codegen.optimization.common.ControlFlowGraph$Companion.build(ControlFlowGraph.kt:160)
	at org.jetbrains.kotlin.codegen.optimization.common.ControlFlowGraph$Companion.build$default(ControlFlowGraph.kt:158)
	at org.jetbrains.kotlin.codegen.coroutines.CoroutineTransformerMethodVisitor.calculateSuspensionPointPredecessorsMapping(CoroutineTransformerMethodVisitor.kt:983)
	at org.jetbrains.kotlin.codegen.coroutines.CoroutineTransformerMethodVisitor.calculateVariablesToCleanup(CoroutineTransformerMethodVisitor.kt:961)
	at org.jetbrains.kotlin.codegen.coroutines.CoroutineTransformerMethodVisitor.spillVariables(CoroutineTransformerMethodVisitor.kt:645)
	at org.jetbrains.kotlin.codegen.coroutines.CoroutineTransformerMethodVisitor.performTransformations(CoroutineTransformerMethodVisitor.kt:120)
	at org.jetbrains.kotlin.codegen.TransformationMethodVisitor.visitEnd(TransformationMethodVisitor.kt:67)
	at org.jetbrains.org.objectweb.asm.tree.MethodNode.accept(MethodNode.java:770)
	at org.jetbrains.kotlin.backend.jvm.codegen.CoroutineCodegenKt.acceptWithStateMachine(CoroutineCodegen.kt:73)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethod(ClassCodegen.kt:439)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generate(ClassCodegen.kt:167)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitClass(ExpressionCodegen.kt:955)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitClass(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.declarations.IrClass.accept(IrClass.kt:72)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:520)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:534)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitor$DefaultImpls.visitBlock(IrElementVisitor.kt:136)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlock(ExpressionCodegen.kt:406)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlock(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrBlock.accept(IrBlock.kt:18)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.gen(ExpressionCodegen.kt:214)
	at org.jetbrains.kotlin.backend.jvm.codegen.IrCallGenerator.genValueAndPut(IrCallGenerator.kt:48)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall$handleParameter(ExpressionCodegen.kt:557)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:585)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:135)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.gen(ExpressionCodegen.kt:214)


